# 🎙️ Анализатор аудиозаписей и транскрибации

Приложение для улучшения качества аудиозаписей, их транскрибации и сравнения результатов с эталонными текстами.

## ✨ Возможности

### 🔧 Улучшение аудио

- **FFMPEG**: Обработка аудио с помощью FFmpeg (нормализация громкости и частотная фильтрация).
- **NoiseReduce**: Спектральный гейтинг для подавления шума.
- **RNNoise**: ИИ-алгоритм подавления шума в реальном времени.
- **DeepFilterNet**: Продвинутое подавление шума на основе глубокого обучения.
- **Спектральное вычитание**: Классический алгоритм обработки.
- **Фильтр Винера**: Адаптивный фильтр для очистки сигнала.

### 📝 Транскрибация

- **WhisperX**: Быстрая транскрибация с выравниванием на уровне слов.
- **Faster-Whisper**: Оптимизированная версия Whisper (до 4x быстрее).
- **OpenAI Whisper**: Оригинальная модель от OpenAI.
- **Wav2Vec2**: Самообучающаяся модель от Facebook.
- **Vosk**: Легковесная оффлайн система распознавания.
- **NeMo ASR**: Предобученные модели от NVIDIA.
- **GigaAM**: Поддержка дополнительных моделей.
- **SpeechBrain**: Открытая платформа для обработки речи.

### 📊 Анализ и сравнение

- **WER (Word Error Rate)**: Процент неправильно распознанных слов.
- **BLEU Score**: Метрика качества на основе n-грамм.
- **Similarity Score**: Семантическая схожесть текстов.
- **Levenshtein Distance**: Расстояние редактирования.
- **Character Accuracy**: Точность на уровне символов.
- Детальный анализ ошибок (замены, вставки, удаления).
- Визуализация результатов.

### 🆚 Сравнение моделей

- **Сравнение нескольких моделей**: Запускайте несколько моделей транскрибации и сравнивайте их производительность.
- **Детальные отчеты**: Получайте подробные отчеты о времени обработки, количестве слов и других метриках для каждой модели.
- **Экспорт результатов**: Экспортируйте результаты сравнения в форматах CSV и TXT.

## 🚀 Установка

### Требования

- Python 3.12+
- [FFmpeg](https://ffmpeg.org/)
- [CUDA](https://developer.nvidia.com/cuda-toolkit) / [cuDNN](https://developer.nvidia.com/cudnn)
- [RNNoise](https://github.com/xiph/rnnoise)

### Шаг 1: Клонирование репозитория

```bash
<NAME_EMAIL>:NeuronsUII/Giper2.git
cd 01_transcribe/EvgeniyDoroshenko/transcribe_app
```

### Шаг 2: Создание виртуального окружения

#### pip
```bash
python -m venv .venv
source .venv/bin/activate
```

### Шаг 3: Установка зависимостей

```bash
pip install -U uv
uv pip install -e .
```

Это установит все необходимые зависимости, включая те, что указаны в `pyproject.toml`.

### Шаг 4: Установка FFmpeg

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install ffmpeg

# macOS
brew install ffmpeg

# Windows
# Скачайте с https://ffmpeg.org/download.html
```

### Шаг 5: Дополнительные модели (опционально)

#### Для Vosk

```bash
# Русская модель
wget https://alphacephei.com/vosk/models/vosk-model-ru-0.42.zip
unzip vosk-model-ru-0.42.zip -d .
```

## 🏃 Запуск приложения

```bash
streamlit run app.py
```

Приложение будет доступно по адресу: http://localhost:8501

## 📖 Использование

### 1. Загрузка аудио

- Перейдите на вкладку "📁 Загрузка аудио".
- Загрузите аудиофайл (поддерживаемые форматы: WAV, MP3, FLAC, OGG, M4A).
- Просмотрите информацию о файле и форму волны.

### 2. Улучшение качества

- Перейдите на вкладку "🔧 Улучшение качества".
- Выберите методы обработки (Один или несколько).
- Настройте параметры для каждого метода (Если необходимо).
- Примените улучшения и сравните результаты.

### 3. Транскрибация

- Перейдите на вкладку "📝 Транскрибация".
- Выберите аудио (исходное или обработанное).
- Выберите систему транскрибации.
- Запустите транскрибацию.

### 4. Сравнение моделей

- Перейдите на вкладку "🆚 Сравнение моделей".
- Выберите аудио для транскрибации.
- Выберите модели для сравнения.
- Запустите сравнение и изучите результаты.

### 5. Сравнение и анализ

- Сравнение возможно только после транскрибации (п.3)
- Перейдите на вкладку "📊 Сравнение и анализ".
- Введите эталонный текст.
- Запустите сравнение.
- Изучите детальные метрики и визуализации.

## 🛠️ Конфигурация

Основные настройки находятся в файле `config/settings.py`:

## 📁 Структура проекта

```
transcript/
├── app.py                          # Главное приложение Streamlit
├── pyproject.toml                  # Зависимости Python
├── config/                         # Конфигурационные файлы
│   └── settings.py                 # Основные настройки
├── modules/                        # Основные модули
│   ├── audio_enhancement/          # Модули улучшения аудио
│   │   ├── base_enhancer.py        # Базовый класс
│   │   └── noise_reduction.py      # Методы подавления шума
│   ├── transcription/              # Модули транскрибации
│   │   ├── base_transcriber.py     # Базовый класс
│   │   ├── gigaam_transcriber.py   # GigaAM 
│   │   ├── nemo_transcriber.py     # Nvidia NeMo
│   │   ├── whisper_transcribers.py # Whisper-based модели
│   │   └── other_transcribers.py   # Другие системы
│   ├── comparison/                 # Модули сравнения
│   │   └── text_comparison.py      # Сравнение текстов
│   └── utils/                      # Утилиты
│       └── audio_utils.py          # Работа с аудио
├── models/                         # Кэш моделей
└── temp/                           # Временные файлы
```
