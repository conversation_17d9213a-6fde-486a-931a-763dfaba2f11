import numpy as np
import logging
from abc import ABC, abstractmethod
from typing import Any

logger = logging.getLogger(__name__)

class BaseAudioEnhancer(ABC):
    """Базовый класс для всех методов улучшения аудио"""

    def __init__(self, name: str, description: str = "", params: dict[str, Any] | None = None):
        self.name = name
        self.description = description
        self.params = params or {}
        self.is_initialized = False

    @abstractmethod
    def initialize(self) -> bool:
        """
        Инициализация метода улучшения (загрузка моделей, настройка параметров)

        Returns:
            bool: True если инициализация успешна
        """
        pass

    @abstractmethod
    def enhance(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """
        Основной метод улучшения аудио

        Args:
            audio_data: Входные аудиоданные
            sample_rate: Частота дискретизации

        Returns:
            np.ndarray: Улучшенные аудиоданные
        """
        pass

    def preprocess(self, audio_data: np.ndarray, sample_rate: int) -> tuple:
        """
        Предварительная обработка аудио

        Args:
            audio_data: Входные аудиоданные
            sample_rate: Частота дискретизации

        Returns:
            tuple: Обработанные аудиоданные и частота дискретизации
        """
        # Базовая проверка входных данных
        if len(audio_data) == 0:
            raise ValueError("Пустые аудиоданные")

        # Нормализация входных данных
        if np.max(np.abs(audio_data)) > 1.0:
            audio_data = audio_data / np.max(np.abs(audio_data))
            logger.info("Аудио нормализовано к диапазону [-1, 1]")

        return audio_data, sample_rate

    def postprocess(self, enhanced_audio: np.ndarray) -> np.ndarray:
        """
        Пост-обработка улучшенного аудио

        Args:
            enhanced_audio: Улучшенные аудиоданные

        Returns:
            np.ndarray: Финальные аудиоданные
        """
        # Базовая нормализация выходных данных
        if np.max(np.abs(enhanced_audio)) > 1.0:
            enhanced_audio = enhanced_audio / np.max(np.abs(enhanced_audio))
            logger.info("Выходное аудио нормализовано")

        return enhanced_audio

    def enhance_with_preprocessing(self, audio_data: np.ndarray, sample_rate: int) -> tuple[np.ndarray, int]:
        """
        Полный пайплайн улучшения с пред- и пост-обработкой.
        
        Returns:
            tuple[np.ndarray, int]: Улучшенные аудиоданные и их новая частота дискретизации.
        """
        if not self.is_initialized:
            if not self.initialize():
                raise RuntimeError(f"Не удалось инициализировать {self.name}")
        
        processed_audio, processed_sr = self.preprocess(audio_data, sample_rate)
        
        # enhance должен возвращать tuple!
        enhanced_audio, final_sr = self.enhance(processed_audio, processed_sr)
        
        final_audio = self.postprocess(enhanced_audio)
        
        logger.info(f"Аудио обработано методом {self.name}")
        return final_audio, final_sr

    def get_info(self) -> dict[str, Any]:
        """
        Возвращает информацию о методе улучшения

        Returns:
            dict[str, Any]: Информация о методе
        """
        return {
            "name": self.name,
            "description": self.description,
            "params": self.params,
            "initialized": self.is_initialized
        }

    def set_params(self, **kwargs) -> None:
        """
        Устанавливает параметры метода

        Args:
            **kwargs: Параметры для установки
        """
        self.params.update(kwargs)
        logger.info(f"Обновлены параметры для {self.name}: {kwargs}")


class EnhancementPipeline:
    """Пайплайн для последовательного применения нескольких методов улучшения"""

    def __init__(self):
        self.enhancers = []

    def add_enhancer(self, enhancer: BaseAudioEnhancer) -> None:
        """
        Добавляет метод улучшения в пайплайн

        Args:
            enhancer: Экземпляр метода улучшения
        """
        self.enhancers.append(enhancer)
        logger.info(f"Добавлен метод улучшения: {enhancer.name}")

    def process(self, left_audio: np.ndarray, right_audio: np.ndarray, sample_rate: int) -> tuple[np.ndarray, np.ndarray, int]:
        """
        Применяет все методы улучшения к обоим каналам отдельно.
        
        Returns:
            tuple[np.ndarray, np.ndarray, int]: (левый_канал, правый_канал, новая_частота)
        """
        current_left = left_audio.copy()
        current_right = right_audio.copy()
        current_sr = sample_rate
        
        for enhancer in self.enhancers:
            logger.info(f"Применяется метод к каналам: {enhancer.name}")
            # Обрабатываем каждый канал отдельно
            current_left, current_sr = enhancer.enhance_with_preprocessing(current_left, current_sr)
            current_right, _ = enhancer.enhance_with_preprocessing(current_right, current_sr)
        
        return current_left, current_right, current_sr


    def get_pipeline_info(self) -> dict[str, Any]:
        """
        Возвращает информацию о пайплайне

        Returns:
            dict[str, Any]: Информация о пайплайне
        """
        return {
            "total_enhancers": len(self.enhancers),
            "enhancers": [enhancer.get_info() for enhancer in self.enhancers]
        }
