from pydub import AudioSegment
import numpy as np
import os
import subprocess
import tempfile
import logging
from typing import Any
from .base_enhancer import BaseAudioEnhancer

logger = logging.getLogger(__name__)


class FFmpegEnhancer(BaseAudioEnhancer):
    """Улучшение аудио с помощью FFmpeg (loudnorm, highpass, lowpass)"""

    def __init__(
        self,
        highpass_freq: int = 120,
        lowpass_freq: int = 2500,
        loudnorm_i: float = -23.0,
        loudnorm_tp: float = -3.0,
        loudnorm_lra: float = 6.0,
        target_sample_rate: int = 16000,
        sample_fmt: str = 's16',
    ):
        super().__init__(
            name="FFmpeg",
            description="Обработка аудио с помощью FFmpeg (нормализация громкости и частотная фильтрация)",
            params={
                "highpass_freq": highpass_freq,
                "lowpass_freq": lowpass_freq,
                "loudnorm_i": loudnorm_i,
                "loudnorm_tp": loudnorm_tp,
                "loudnorm_lra": loudnorm_lra,
                "target_sample_rate": target_sample_rate,
                "sample_fmt": sample_fmt,
            },
        )

    def initialize(self) -> bool:
        """Инициализация FFmpeg (проверка доступности)"""
        try:
            result = subprocess.run(
                ['ffmpeg', '-version'],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                check=True,
            )
            self.is_initialized = True
            logger.info("FFmpeg инициализирован")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError) as e:
            logger.error(f"FFmpeg не найден или не работает: {e}")
            return False

    def enhance(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> tuple[np.ndarray, int]:
        """Улучшение аудио с помощью FFmpeg"""
        try:
            import soundfile as sf

            target_sr = self.params["target_sample_rate"]
            
            # Логируем исходные параметры
            original_duration = len(audio_data) / sample_rate
            logger.info(f"ИСХОДНОЕ АУДИО: {len(audio_data)} сэмплов, {sample_rate} Гц, {original_duration:.2f} сек")

            # Создаем временные файлы
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as input_file:
                input_path = input_file.name

            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as output_file:
                output_path = output_file.name

            try:
                # Сохраняем входное аудио во временный файл
                sf.write(input_path, audio_data, sample_rate)

                # Строим команду FFmpeg
                cmd = self._build_ffmpeg_command(input_path, output_path)
                
                logger.info(f"FFmpeg команда: {' '.join(cmd)}")

                # Выполняем обработку
                result = subprocess.run(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    check=True,
                    text=True,
                )

                # ВАЖНО: логируем stderr для диагностики
                if result.stderr:
                    logger.info(f"FFmpeg stderr: {result.stderr}")

                # Загружаем обработанное аудио
                enhanced_audio, output_sr = sf.read(output_path)
                
                actual_duration = len(enhanced_audio) / output_sr
                logger.info(f"ПОСЛЕ FFmpeg: {len(enhanced_audio)} сэмплов, {output_sr} Гц, {actual_duration:.2f} сек")

                # ПРИНУДИТЕЛЬНОЕ ВОССТАНОВЛЕНИЕ ПРАВИЛЬНОЙ ДЛИТЕЛЬНОСТИ
                # Вычисляем правильную длину в сэмплах для новой частоты
                expected_samples = int(original_duration * target_sr)
                
                logger.info(f"ОЖИДАЕМЫЕ сэмплы: {expected_samples}, ФАКТИЧЕСКИЕ: {len(enhanced_audio)}")

                if len(enhanced_audio) != expected_samples:
                    logger.warning(f"НЕСООТВЕТСТВИЕ ДЛИТЕЛЬНОСТИ! Корректируем с {len(enhanced_audio)} до {expected_samples}")
                    
                    if len(enhanced_audio) > expected_samples:
                        # Обрезаем лишнее
                        enhanced_audio = enhanced_audio[:expected_samples]
                    else:
                        # Дополняем нулями
                        pad = expected_samples - len(enhanced_audio)
                        enhanced_audio = np.pad(enhanced_audio, (0, pad), mode='constant')

                final_duration = len(enhanced_audio) / output_sr
                logger.info(f"ИТОГО: {len(enhanced_audio)} сэмплов, {output_sr} Гц, {final_duration:.2f} сек")

                return enhanced_audio, output_sr

            finally:
                # Удаляем временные файлы
                for temp_path in [input_path, output_path]:
                    if os.path.exists(temp_path):
                        try:
                            os.unlink(temp_path)
                        except Exception as e:
                            logger.warning(f"Не удалось удалить временный файл {temp_path}: {e}")

        except subprocess.CalledProcessError as e:
            logger.error(f"Ошибка выполнения FFmpeg: {e.stderr}")
            return audio_data, sample_rate
        except Exception as e:
            logger.error(f"Ошибка в FFmpegEnhancer: {e}")
            return audio_data, sample_rate

    def _build_ffmpeg_command(self, input_path: str, output_path: str) -> list:
        """Строит команду FFmpeg с фильтрами"""
        highpass = self.params["highpass_freq"]
        lowpass = self.params["lowpass_freq"]
        loudnorm_i = self.params["loudnorm_i"]
        loudnorm_tp = self.params["loudnorm_tp"]
        loudnorm_lra = self.params["loudnorm_lra"]
        target_sr = self.params["target_sample_rate"]
        sample_fmt = self.params["sample_fmt"]

        # ИСПОЛЬЗУЕМ -af ВМЕСТО -filter_complex для простых аудио фильтров
        # И явно указываем выходную частоту в aresample
        audio_filter = (
            f"loudnorm=I={loudnorm_i}:TP={loudnorm_tp}:LRA={loudnorm_lra},"
            f"highpass=f={highpass},"
            f"lowpass=f={lowpass},"
            f"aresample=resampler=soxr:osr={target_sr}"  # ЯВНО УКАЗЫВАЕМ ВЫХОДНУЮ ЧАСТОТУ
        )

        cmd = [
            'ffmpeg',
            '-y',
            '-i', input_path,
            '-af', audio_filter,  # ИСПОЛЬЗУЕМ -af ВМЕСТО -filter_complex
            '-ar', str(target_sr),
            '-ac', '1',
            '-sample_fmt', sample_fmt,
            '-c:a', 'pcm_s16le',
            output_path,
        ]

        return cmd

    # Остальные методы остаются без изменений...
    def get_filter_description(self) -> str:
        highpass = self.params["highpass_freq"]
        lowpass = self.params["lowpass_freq"]
        loudnorm_i = self.params["loudnorm_i"]
        target_sr = self.params["target_sample_rate"]
        
        return (
            f"Loudnorm (I={loudnorm_i}dB), "
            f"Highpass ({highpass}Hz), "
            f"Lowpass ({lowpass}Hz), "
            f"Resample ({target_sr}Hz)"
        )

    def set_loudnorm_params(
        self, integrated: float = -23.0, true_peak: float = -3.0, lra: float = 6.0
    ) -> None:
        self.params.update(
            {"loudnorm_i": integrated, "loudnorm_tp": true_peak, "loudnorm_lra": lra}
        )
        logger.info(
            f"Обновлены параметры loudnorm: I={integrated}, TP={true_peak}, LRA={lra}"
        )

    def set_filter_params(self, highpass: int = 120, lowpass: int = 2500) -> None:
        self.params.update({"highpass_freq": highpass, "lowpass_freq": lowpass})
        logger.info(f"Обновлены параметры фильтров: HP={highpass}Hz, LP={lowpass}Hz")

    def set_output_params(
        self, sample_rate: int = 16000, sample_fmt: str = 's16'
    ) -> None:
        self.params.update(
            {"target_sample_rate": sample_rate, "sample_fmt": sample_fmt}
        )
        logger.info(f"Обновлены параметры вывода: SR={sample_rate}Hz, fmt={sample_fmt}")

    def get_info(self) -> dict[str, Any]:
        info = super().get_info()
        info["filter_description"] = self.get_filter_description()
        return info



class NoiseReduceEnhancer(BaseAudioEnhancer):
    """Улучшение аудио с помощью noisereduce (спектральный гейтинг)"""

    def __init__(self, stationary: bool = True, prop_decrease: float = 0.8):
        super().__init__(
            name="NoiseReduce",
            description="Спектральный гейтинг для подавления шума",
            params={"stationary": stationary, "prop_decrease": prop_decrease},
        )
        self.nr = None

    def initialize(self) -> bool:
        """Инициализация noisereduce"""
        try:
            import noisereduce as nr

            self.nr = nr
            self.is_initialized = True
            logger.info("NoiseReduce инициализирован")
            return True
        except ImportError as e:
            logger.error(f"Не удалось импортировать noisereduce: {e}")
            return False

    def enhance(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> tuple[np.ndarray, int]:
        """
        Подавление шума с помощью noisereduce

        Args:
            audio_data: Входные аудиоданные
            sample_rate: Частота дискретизации

        Returns:
            np.ndarray: Аудио с подавленным шумом
        """
        try:
            enhanced_audio = self.nr.reduce_noise(
                y=audio_data,
                sr=sample_rate,
                stationary=self.params["stationary"],
                prop_decrease=self.params["prop_decrease"],
            )
            logger.info("Шум подавлен с помощью NoiseReduce")
            return enhanced_audio, sample_rate
        except Exception as e:
            logger.error(f"Ошибка в NoiseReduce: {e}")
            return audio_data


class SpectralSubtractionEnhancer(BaseAudioEnhancer):
    """Спектральное вычитание для подавления шума"""

    def __init__(
        self,
        alpha: float = 2.0,
        beta: float = 0.01,
        frame_size: int = 1024,
        hop_size: int = 256,
    ):
        super().__init__(
            name="Spectral Subtraction",
            description="Классический алгоритм спектрального вычитания",
            params={
                "alpha": alpha,
                "beta": beta,
                "frame_size": frame_size,
                "hop_size": hop_size,
            },
        )

    def initialize(self) -> bool:
        """Инициализация спектрального вычитания"""
        try:
            import scipy.signal
            import librosa

            self.scipy_signal = scipy.signal
            self.librosa = librosa
            self.is_initialized = True
            logger.info("Spectral Subtraction инициализирован")
            return True
        except ImportError as e:
            logger.error(f"Не удалось импортировать библиотеки: {e}")
            return False

    def enhance(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> tuple[np.ndarray, int]:
        """
        Спектральное вычитание

        Args:
            audio_data: Входные аудиоданные
            sample_rate: Частота дискретизации

        Returns:
            np.ndarray: Обработанное аудио
        """
        try:
            # Параметры
            frame_size = self.params["frame_size"]
            hop_size = self.params["hop_size"]
            alpha = self.params["alpha"]
            beta = self.params["beta"]

            # STFT
            stft = self.librosa.stft(audio_data, n_fft=frame_size, hop_length=hop_size)
            magnitude = np.abs(stft)
            phase = np.angle(stft)

            # Оценка шума (используем первые 10% как шум)
            noise_frames = int(0.1 * magnitude.shape[1])
            noise_magnitude = np.mean(
                magnitude[:, :noise_frames], axis=1, keepdims=True
            )

            # Спектральное вычитание
            enhanced_magnitude = magnitude - alpha * noise_magnitude

            # Применяем beta как минимальный порог
            enhanced_magnitude = np.maximum(enhanced_magnitude, beta * magnitude)

            # Восстанавливаем аудио
            enhanced_stft = enhanced_magnitude * np.exp(1j * phase)
            enhanced_audio = self.librosa.istft(enhanced_stft, hop_length=hop_size)

            logger.info("Применено спектральное вычитание")
            return enhanced_audio, sample_rate

        except Exception as e:
            logger.error(f"Ошибка в спектральном вычитании: {e}")
            return audio_data


class WienerFilterEnhancer(BaseAudioEnhancer):
    """Адаптивный фильтр Винера"""

    def __init__(self, noise_power_estimate: float = 0.1):
        super().__init__(
            name="Wiener Filter",
            description="Адаптивный фильтр Винера для подавления шума",
            params={"noise_power_estimate": noise_power_estimate},
        )

    def initialize(self) -> bool:
        """Инициализация фильтра Винера"""
        try:
            import scipy.signal
            import librosa

            self.scipy_signal = scipy.signal
            self.librosa = librosa
            self.is_initialized = True
            logger.info("Wiener Filter инициализирован")
            return True
        except ImportError as e:
            logger.error(f"Не удалось импортировать библиотеки: {e}")
            return False

    def enhance(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> tuple[np.ndarray, int]:
        """
        Применение фильтра Винера

        Args:
            audio_data: Входные аудиоданные
            sample_rate: Частота дискретизации

        Returns:
            np.ndarray: Отфильтрованное аудио
        """
        try:
            # STFT
            stft = self.librosa.stft(audio_data)
            magnitude = np.abs(stft)
            phase = np.angle(stft)

            # Оценка мощности сигнала
            signal_power = magnitude**2

            # Оценка мощности шума
            noise_power = self.params["noise_power_estimate"] * np.mean(signal_power)

            # Фильтр Винера
            wiener_gain = signal_power / (signal_power + noise_power)
            enhanced_magnitude = magnitude * wiener_gain

            # Восстановление аудио
            enhanced_stft = enhanced_magnitude * np.exp(1j * phase)
            enhanced_audio = self.librosa.istft(enhanced_stft)

            logger.info("Применен фильтр Винера")
            return enhanced_audio, sample_rate

        except Exception as e:
            logger.error(f"Ошибка в фильтре Винера: {e}")
            return audio_data


class DeepFilterNetEnhancer(BaseAudioEnhancer):
    """Улучшение аудио с помощью DeepFilterNet (если доступен)"""

    def __init__(self, model_name: str = "DeepFilterNet2"):
        super().__init__(
            name="DeepFilterNet",
            description="ИИ-алгоритм подавления шума на основе глубокого обучения",
            params={"model_name": model_name},
        )
        self.model = None
        self.df_state = None
        self.enhance_func = None

    def initialize(self) -> bool:
        """Инициализация DeepFilterNet"""
        try:
            from df import enhance, init_df

            self.model, self.df_state, _ = (
                init_df()
            )  # Без параметров согласно текущему API
            self.enhance_func = enhance
            self.is_initialized = True
            logger.info(f"DeepFilterNet ({self.params['model_name']}) инициализирован")
            return True
        except ImportError as e:
            logger.error(f"DeepFilterNet не доступен: {e}")
            return False
        except Exception as e:
            logger.error(f"Ошибка инициализации DeepFilterNet: {e}")
            return False

    def enhance(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> tuple[np.ndarray, int]:
        """Подавление шума с помощью DeepFilterNet"""
        try:
            import torch
            import librosa

            target_sr = 48000

            # Ресемплинг к 48 кГц
            if sample_rate != target_sr:
                audio_resampled = librosa.resample(
                    audio_data, orig_sr=sample_rate, target_sr=target_sr
                )
            else:
                audio_resampled = audio_data

            # Создаем тензор на CPU и добавляем batch-размерность
            audio_tensor = torch.from_numpy(
                audio_resampled.astype("float32")
            ).unsqueeze(
                0
            )  # [1, T]

            # Вызов DeepFilterNet
            enhanced_tensor = self.enhance_func(self.model, self.df_state, audio_tensor)

            # Конвертируем результат в numpy и убираем batch-размерность
            if isinstance(enhanced_tensor, torch.Tensor):
                enhanced_np_48k = (
                    enhanced_tensor.squeeze(0).detach().cpu().numpy()
                )  # [T]
            else:
                enhanced_np_48k = np.asarray(enhanced_tensor, dtype=np.float32)
                if enhanced_np_48k.ndim > 1:
                    enhanced_np_48k = enhanced_np_48k.squeeze(0)

            # Ресемплинг обратно
            if sample_rate != target_sr:
                enhanced_np = librosa.resample(
                    enhanced_np_48k, orig_sr=target_sr, target_sr=sample_rate
                )
            else:
                enhanced_np = enhanced_np_48k

            # Санитация и нормализация
            if not np.isfinite(enhanced_np).all():
                enhanced_np = np.nan_to_num(
                    enhanced_np, nan=0.0, posinf=0.0, neginf=0.0
                )

            max_abs = np.max(np.abs(enhanced_np)) if enhanced_np.size > 0 else 0.0
            if max_abs > 1.0:
                enhanced_np = enhanced_np / max_abs

            return enhanced_np.astype("float32"), sample_rate

        except Exception as e:
            logger.error(f"Ошибка в DeepFilterNet: {e}")
            return audio_data, sample_rate


class RNNoiseEnhancer(BaseAudioEnhancer):
    """Шумоподавление с использованием библиотеки Xiph’s RNNoise."""

    def __init__(self) -> None:
        super().__init__(
            name="RNNoise",
            description="Подавление шума на основе рекуррентной нейронной сети (RNNoise)",
            params={},
        )
        self._backend: str | None = None
        self._rn = None

    def initialize(self) -> bool:
        """Инициализация бэкенда rnnoise_wrapper."""
        try:
            import rnnoise_wrapper as rw

            self._rn = rw.RNNoise()
            self._backend = "rnnoise_wrapper"
            self.is_initialized = True
            logger.info("Бэкенд RNNoise (rnnoise_wrapper) инициализирован")
            return True
        except Exception as exc:
            logger.warning(
                "Библиотека rnnoise_wrapper не найдена или не удалось её инициализировать: %s",
                exc,
            )
            return False

    def _numpy_to_audiosegment(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> AudioSegment:
        """Конвертирует numpy-массив в объект AudioSegment."""
        if audio_data.dtype in [np.float32, np.float64]:
            audio_data = (audio_data * 32767).astype(np.int16)
        return AudioSegment(
            data=audio_data.tobytes(),
            sample_width=audio_data.dtype.itemsize,
            frame_rate=sample_rate,
            channels=1,
        )

    def _audiosegment_to_numpy(self, audio_segment: AudioSegment) -> np.ndarray:
        """Конвертирует AudioSegment обратно в numpy-массив."""
        samples = np.array(audio_segment.get_array_of_samples())
        if samples.dtype == np.int16:
            return samples.astype(np.float32) / 32767.0
        return samples

    def enhance(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> tuple[np.ndarray, int]:
        if not self.is_initialized and not self.initialize():
            logger.warning("RNNoise недоступен, возвращается исходное аудио")
            return audio_data, sample_rate

        target_sr = 48000
        original_length_at_target_sr = int(len(audio_data) * (target_sr / sample_rate))

        if sample_rate != target_sr:
            import librosa

            processed_audio = librosa.resample(
                audio_data, orig_sr=sample_rate, target_sr=target_sr
            )
        else:
            processed_audio = audio_data

        original_length = len(processed_audio)

        frame_len = 480
        pad_needed = (-original_length) % frame_len
        padded_audio = (
            np.pad(processed_audio, (0, pad_needed), 'constant')
            if pad_needed > 0
            else processed_audio
        )

        out_audio = np.empty_like(padded_audio)

        for i in range(0, len(padded_audio), frame_len):
            frame_np = padded_audio[i : i + frame_len]
            frame_as = self._numpy_to_audiosegment(frame_np, target_sr)
            try:
                enhanced_frame_as = self._rn.filter(audio=frame_as)
            except TypeError:
                enhanced_frame_as = self._rn.filter(audio=frame_as.raw_data)
            enhanced_frame_np = self._audiosegment_to_numpy(enhanced_frame_as)
            out_audio[i : i + len(enhanced_frame_np)] = enhanced_frame_np

        out_audio = out_audio[:original_length]

        # --- ДОБАВЛЯЕМ РЕСЕМПЛИНГ ОБРАТНО ---
        if target_sr != sample_rate:
            import librosa

            out_audio = librosa.resample(
                out_audio, orig_sr=target_sr, target_sr=sample_rate
            )

        return out_audio, sample_rate
