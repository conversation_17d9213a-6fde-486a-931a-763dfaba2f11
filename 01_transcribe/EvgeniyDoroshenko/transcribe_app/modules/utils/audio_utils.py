import numpy as np
import librosa
import soundfile as sf
import logging
from pathlib import Path
from typing import Tuple, Optional, Union, List
import tempfile
import os

logger = logging.getLogger(__name__)

class AudioProcessor:
    """Класс для обработки аудиофайлов"""
    
    def __init__(self, target_sample_rate: int = 16000):
        self.target_sample_rate = target_sample_rate
        
    def load_audio(self, file_path: Union[str, Path]) -> Tuple[Tuple[np.ndarray, np.ndarray], int]:
        """
        Загрузка аудиофайла с разделением на каналы
        
        Returns:
            Tuple[Tuple[np.ndarray, np.ndarray], int]: ((левый_канал, правый_канал), частота)
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                raise FileNotFoundError(f"Файл не найден: {file_path}")
            
            # Загружаем аудио БЕЗ mono=True для сохранения стерео
            audio_data, sample_rate = librosa.load(
                str(file_path), 
                sr=None, 
                mono=False
            )
            
            # Разделяем на каналы
            if len(audio_data.shape) == 1:
                # Моно файл - дублируем на оба канала
                left_channel = audio_data
                right_channel = audio_data
            else:
                # Стерео файл
                left_channel = audio_data[0]
                right_channel = audio_data[1]
            
            logger.info(f"Загружен аудиофайл: {file_path.name}, "
                    f"длительность: {len(left_channel)/sample_rate:.2f}с, "
                    f"частота: {sample_rate}Гц, "
                    f"каналы: левый (менеджер), правый (клиент)")
            
            return (left_channel, right_channel), sample_rate
            
        except Exception as e:
            logger.error(f"Ошибка загрузки аудио {file_path}: {e}")
            raise

    
    def save_audio(self, audio_data: np.ndarray, sample_rate: int, 
                   file_path: Union[str, Path], format: str = 'WAV') -> None:
        """
        Сохранение аудиофайла
        
        Args:
            audio_data: Аудиоданные
            sample_rate: Частота дискретизации
            file_path: Путь для сохранения
            format: Формат файла
        """
        try:
            sf.write(str(file_path), audio_data, sample_rate, format=format)
            logger.info(f"Аудио сохранено: {file_path}")
            
        except Exception as e:
            logger.error(f"Ошибка сохранения аудио: {e}")
            raise
    
    def resample_audio(self, audio_data: np.ndarray, 
                      orig_sr: int, target_sr: int) -> np.ndarray:
        """
        Изменение частоты дискретизации
        
        Args:
            audio_data: Исходные аудиоданные
            orig_sr: Исходная частота
            target_sr: Целевая частота
            
        Returns:
            np.ndarray: Аудио с новой частотой дискретизации
        """
        if orig_sr == target_sr:
            return audio_data
        
        try:
            resampled_audio = librosa.resample(
                audio_data, 
                orig_sr=orig_sr, 
                target_sr=target_sr
            )
            
            logger.debug(f"Аудио ресэмплировано: {orig_sr}Гц -> {target_sr}Гц")
            return resampled_audio
            
        except Exception as e:
            logger.error(f"Ошибка ресэмплирования: {e}")
            raise
    
    def normalize_audio(self, audio_data: np.ndarray, 
                       target_level: float = 0.9) -> np.ndarray:
        """
        Нормализация аудио
        
        Args:
            audio_data: Аудиоданные
            target_level: Целевой уровень (0-1)
            
        Returns:
            np.ndarray: Нормализованное аудио
        """
        if len(audio_data) == 0:
            return audio_data
        
        max_val = np.max(np.abs(audio_data))
        if max_val > 0:
            normalized_audio = audio_data / max_val * target_level
            logger.debug("Аудио нормализовано")
            return normalized_audio
        
        return audio_data
    
    def trim_silence(self, audio_data: np.ndarray, sample_rate: int,
                    top_db: int = 20) -> np.ndarray:
        """
        Обрезка тишины в начале и конце
        
        Args:
            audio_data: Аудиоданные
            sample_rate: Частота дискретизации
            top_db: Порог тишины в дБ
            
        Returns:
            np.ndarray: Обрезанное аудио
        """
        try:
            trimmed_audio, _ = librosa.effects.trim(
                audio_data, 
                top_db=top_db
            )
            
            original_duration = len(audio_data) / sample_rate
            trimmed_duration = len(trimmed_audio) / sample_rate
            
            logger.debug(f"Обрезана тишина: {original_duration:.2f}с -> {trimmed_duration:.2f}с")
            return trimmed_audio
            
        except Exception as e:
            logger.error(f"Ошибка обрезки тишины: {e}")
            return audio_data
    
    def split_audio_channels(self, audio_data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Разделение стерео аудио на каналы
        
        Args:
            audio_data: Стерео аудиоданные
            
        Returns:
            Tuple[np.ndarray, np.ndarray]: Левый и правый каналы
        """
        if len(audio_data.shape) == 1:
            # Моно аудио - дублируем канал
            return audio_data, audio_data
        
        if audio_data.shape[0] == 2:
            left_channel = audio_data[0]
            right_channel = audio_data[1]
        else:
            # Транспонируем если каналы в столбцах
            audio_data = audio_data.T
            left_channel = audio_data[:, 0]
            right_channel = audio_data[:, 1] if audio_data.shape[1] > 1 else audio_data[:, 0]
        
        logger.debug("Аудио разделено на каналы")
        return left_channel, right_channel
    
    def get_audio_features(self, audio_data: np.ndarray, 
                          sample_rate: int) -> dict:
        """
        Извлечение характеристик аудио
        
        Args:
            audio_data: Аудиоданные
            sample_rate: Частота дискретизации
            
        Returns:
            dict: Словарь с характеристиками
        """
        try:
            features = {}
            
            # Основные характеристики
            features['duration'] = len(audio_data) / sample_rate
            features['sample_rate'] = sample_rate
            features['samples'] = len(audio_data)
            
            # Статистические характеристики
            features['rms'] = float(np.sqrt(np.mean(audio_data**2)))
            features['max_amplitude'] = float(np.max(np.abs(audio_data)))
            features['zero_crossing_rate'] = float(np.mean(librosa.feature.zero_crossing_rate(audio_data)))
            
            # Спектральные характеристики
            spectral_centroids = librosa.feature.spectral_centroid(y=audio_data, sr=sample_rate)
            features['spectral_centroid'] = float(np.mean(spectral_centroids))
            
            spectral_rolloff = librosa.feature.spectral_rolloff(y=audio_data, sr=sample_rate)
            features['spectral_rolloff'] = float(np.mean(spectral_rolloff))
            
            # MFCC характеристики
            mfccs = librosa.feature.mfcc(y=audio_data, sr=sample_rate, n_mfcc=13)
            features['mfcc_mean'] = [float(x) for x in np.mean(mfccs, axis=1)]
            
            logger.debug("Извлечены характеристики аудио")
            return features
            
        except Exception as e:
            logger.error(f"Ошибка извлечения характеристик: {e}")
            return {}
    
    def detect_voice_activity(self, audio_data: np.ndarray, 
                             sample_rate: int, frame_length: int = 2048,
                             hop_length: int = 512) -> np.ndarray:
        """
        Обнаружение речевой активности (Voice Activity Detection)
        
        Args:
            audio_data: Аудиоданные
            sample_rate: Частота дискретизации
            frame_length: Длина фрейма
            hop_length: Шаг сдвига
            
        Returns:
            np.ndarray: Маска речевой активности
        """
        try:
            # Вычисляем RMS энергию
            rms = librosa.feature.rms(
                y=audio_data, 
                frame_length=frame_length, 
                hop_length=hop_length
            )[0]
            
            # Простое пороговое определение
            threshold = np.mean(rms) * 0.3
            voice_activity = rms > threshold
            
            logger.debug("Выполнено обнаружение речевой активности")
            return voice_activity
            
        except Exception as e:
            logger.error(f"Ошибка обнаружения речевой активности: {e}")
            return np.ones(len(audio_data) // hop_length, dtype=bool)
    
    def apply_preemphasis(self, audio_data: np.ndarray, 
                         coeff: float = 0.97) -> np.ndarray:
        """
        Применение предустановки (preemphasis)
        
        Args:
            audio_data: Аудиоданные
            coeff: Коэффициент предустановки
            
        Returns:
            np.ndarray: Обработанное аудио
        """
        try:
            emphasized_audio = np.append(audio_data[0], audio_data[1:] - coeff * audio_data[:-1])
            logger.debug("Применена предустановка")
            return emphasized_audio
            
        except Exception as e:
            logger.error(f"Ошибка применения предустановки: {e}")
            return audio_data
    
    def segment_audio(self, audio_data: np.ndarray, sample_rate: int,
                     segment_duration: float = 10.0) -> List[np.ndarray]:
        """
        Разбиение аудио на сегменты
        
        Args:
            audio_data: Аудиоданные
            sample_rate: Частота дискретизации
            segment_duration: Длительность сегмента в секундах
            
        Returns:
            List[np.ndarray]: Список сегментов
        """
        try:
            segment_samples = int(segment_duration * sample_rate)
            segments = []
            
            for i in range(0, len(audio_data), segment_samples):
                segment = audio_data[i:i + segment_samples]
                if len(segment) > 0:
                    segments.append(segment)
            
            logger.debug(f"Аудио разбито на {len(segments)} сегментов")
            return segments
            
        except Exception as e:
            logger.error(f"Ошибка сегментации аудио: {e}")
            return [audio_data]
    
    def compute_snr(self, clean_audio: np.ndarray, 
                   noisy_audio: np.ndarray) -> float:
        """
        Вычисление отношения сигнал/шум (SNR)
        
        Args:
            clean_audio: Чистое аудио
            noisy_audio: Зашумленное аудио
            
        Returns:
            float: SNR в дБ
        """
        try:
            # Выравниваем длины
            min_length = min(len(clean_audio), len(noisy_audio))
            clean_audio = clean_audio[:min_length]
            noisy_audio = noisy_audio[:min_length]
            
            # Вычисляем шум
            noise = noisy_audio - clean_audio
            
            # Мощность сигнала и шума
            signal_power = np.mean(clean_audio**2)
            noise_power = np.mean(noise**2)
            
            if noise_power == 0:
                return float('inf')
            
            snr = 10 * np.log10(signal_power / noise_power)
            
            logger.debug(f"SNR: {snr:.2f} дБ")
            return float(snr)
            
        except Exception as e:
            logger.error(f"Ошибка вычисления SNR: {e}")
            return 0.0


class AudioConverter:
    """Класс для конвертации аудиоформатов"""
    
    def __init__(self):
        pass
    
    def convert_format(self, input_path: Union[str, Path], 
                      output_path: Union[str, Path],
                      target_format: str = 'wav',
                      target_sr: Optional[int] = None) -> bool:
        """
        Конвертация аудиофайла в другой формат
        
        Args:
            input_path: Входной файл
            output_path: Выходной файл
            target_format: Целевой формат
            target_sr: Целевая частота дискретизации
            
        Returns:
            bool: Успешность конвертации
        """
        try:
            processor = AudioProcessor()
            audio_data, sample_rate = processor.load_audio(input_path)
            
            # Ensure audio_data is a single numpy array
            if isinstance(audio_data, tuple):
                audio_data = audio_data[0]
            
            if target_sr and target_sr != sample_rate:
                audio_data = processor.resample_audio(audio_data, sample_rate, target_sr)
                sample_rate = target_sr
            
            processor.save_audio(audio_data, sample_rate, output_path, target_format)
            
            logger.info(f"Конвертирован файл: {input_path} -> {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка конвертации: {e}")
            return False
    
    def batch_convert(self, input_dir: Union[str, Path],
                     output_dir: Union[str, Path],
                     target_format: str = 'wav',
                     target_sr: Optional[int] = None) -> int:
        """
        Пакетная конвертация файлов
        
        Args:
            input_dir: Директория с входными файлами
            output_dir: Директория для выходных файлов
            target_format: Целевой формат
            target_sr: Целевая частота дискретизации
            
        Returns:
            int: Количество конвертированных файлов
        """
        input_dir = Path(input_dir)
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        audio_extensions = ['.wav', '.mp3', '.flac', '.ogg', '.m4a']
        audio_files = []
        
        for ext in audio_extensions:
            audio_files.extend(input_dir.glob(f'*{ext}'))
            audio_files.extend(input_dir.glob(f'*{ext.upper()}'))
        
        converted_count = 0
        
        for audio_file in audio_files:
            output_file = output_dir / f"{audio_file.stem}.{target_format}"
            
            if self.convert_format(audio_file, output_file, target_format, target_sr):
                converted_count += 1
        
        logger.info(f"Конвертировано {converted_count} из {len(audio_files)} файлов")
        return converted_count


def validate_audio_file(file_path: Union[str, Path]) -> dict:
    """
    Валидация аудиофайла
    
    Args:
        file_path: Путь к файлу
        
    Returns:
        dict: Результат валидации
    """
    result = {
        'valid': False,
        'errors': [],
        'warnings': [],
        'info': {}
    }
    
    try:
        file_path = Path(file_path)
        
        # Проверка существования файла
        if not file_path.exists():
            result['errors'].append("Файл не существует")
            return result
        
        # Проверка размера файла
        file_size = file_path.stat().st_size
        result['info']['file_size_mb'] = file_size / (1024 * 1024)
        
        if file_size == 0:
            result['errors'].append("Файл пустой")
            return result
        
        if file_size > 500 * 1024 * 1024:  # 500 МБ
            result['warnings'].append("Файл очень большой (>500MB)")
        
        # Попытка загрузки аудио
        processor = AudioProcessor()
        audio_data, sample_rate = processor.load_audio(file_path)
        
        # Проверка основных параметров
        duration = len(audio_data) / sample_rate
        result['info']['duration'] = duration
        result['info']['sample_rate'] = sample_rate
        result['info']['samples'] = len(audio_data)
        
        if duration == 0:
            result['errors'].append("Аудио имеет нулевую длительность")
            return result
        
        if duration > 3600:  # 1 час
            result['warnings'].append("Аудио очень длинное (>1 час)")
        
        if sample_rate < 8000:
            result['warnings'].append("Низкая частота дискретизации (<8kHz)")
        
        # Проверка на наличие данных
        if np.all(audio_data == 0):
            result['errors'].append("Аудио содержит только тишину")
            return result
        
        result['valid'] = True
        logger.info(f"Файл {file_path.name} прошел валидацию")
        
    except Exception as e:
        result['errors'].append(f"Ошибка при валидации: {str(e)}")
        logger.error(f"Ошибка валидации файла {file_path}: {e}")
    
    return result
