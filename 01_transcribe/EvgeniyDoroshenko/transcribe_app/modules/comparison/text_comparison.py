import numpy as np
import logging
from typing import Any
from dataclasses import dataclass
import re
import difflib

logger = logging.getLogger(__name__)

@dataclass
class ComparisonResult:
    """Результат сравнения транскрипций"""

    wer: float  # Word Error Rate
    bleu_score: float  # BLEU Score
    similarity_score: float  # Similarity Score
    levenshtein_distance: int  # Levenshtein Distance
    character_accuracy: float  # Character-level accuracy

    # Детальная информация
    word_accuracy: float
    insertion_rate: float
    deletion_rate: float
    substitution_rate: float

    # Текстовое представление различий
    differences_html: str
    aligned_text: str

    # Статистика
    reference_word_count: int
    hypothesis_word_count: int
    reference_char_count: int
    hypothesis_char_count: int


class TextComparator:
    """Класс для сравнения текстов транскрипций"""

    def __init__(self):
        self.jiwer = None
        self.nltk = None
        self._initialize_libraries()

    def _initialize_libraries(self) -> None:
        """Инициализация библиотек для сравнения"""
        try:
            import jiwer
            self.jiwer = jiwer
            logger.info("jiwer инициализирован")
        except ImportError:
            logger.warning("jiwer не доступен, WER будет вычисляться простым методом")

        try:
            import nltk
            self.nltk = nltk
            # Загружаем необходимые данные NLTK
            try:
                nltk.data.find('tokenizers/punkt')
            except LookupError:
                nltk.download('punkt', quiet=True)
            logger.info("NLTK инициализирован")
        except ImportError:
            logger.warning("NLTK не доступен, некоторые функции будут ограничены")

    def compare_texts(self, reference: str, hypothesis: str) -> ComparisonResult:
        """
        Полное сравнение двух текстов

        Args:
            reference: Эталонный текст
            hypothesis: Транскрибированный текст

        Returns:
            ComparisonResult: Результат сравнения
        """
        # Предобработка текстов
        ref_clean = self._preprocess_text(reference)
        hyp_clean = self._preprocess_text(hypothesis)

        # Вычисляем различные метрики
        wer = self._calculate_wer(ref_clean, hyp_clean)
        bleu = self._calculate_bleu(ref_clean, hyp_clean)
        similarity = self._calculate_similarity(ref_clean, hyp_clean)
        levenshtein = self._calculate_levenshtein(ref_clean, hyp_clean)
        char_accuracy = self._calculate_character_accuracy(ref_clean, hyp_clean)

        # Детальный анализ ошибок
        word_stats = self._analyze_word_errors(ref_clean, hyp_clean)

        # Создаем визуализацию различий
        diff_html = self._create_html_diff(reference, hypothesis)
        aligned = self._create_aligned_text(ref_clean, hyp_clean)

        # Статистика текстов
        ref_words = len(ref_clean.split())
        hyp_words = len(hyp_clean.split())
        ref_chars = len(ref_clean)
        hyp_chars = len(hyp_clean)

        return ComparisonResult(
            wer=wer,
            bleu_score=bleu,
            similarity_score=similarity,
            levenshtein_distance=levenshtein,
            character_accuracy=char_accuracy,
            word_accuracy=word_stats["accuracy"],
            insertion_rate=word_stats["insertion_rate"],
            deletion_rate=word_stats["deletion_rate"],
            substitution_rate=word_stats["substitution_rate"],
            differences_html=diff_html,
            aligned_text=aligned,
            reference_word_count=ref_words,
            hypothesis_word_count=hyp_words,
            reference_char_count=ref_chars,
            hypothesis_char_count=hyp_chars
        )

    def _preprocess_text(self, text: str) -> str:
        """
        Предобработка текста для сравнения

        Args:
            text: Исходный текст

        Returns:
            str: Обработанный текст
        """
        if not text:
            return ""

        # Приводим к нижнему регистру
        text = text.lower()

        # Удаляем лишние пробелы
        text = re.sub(r'\s+', ' ', text)

        # Удаляем знаки препинания (опционально)
        text = re.sub(r'[^\w\s]', '', text)

        return text.strip()

    def _calculate_wer(self, reference: str, hypothesis: str) -> float:
        """
        Вычисление Word Error Rate (WER)

        Args:
            reference: Эталонный текст
            hypothesis: Транскрибированный текст

        Returns:
            float: WER значение
        """
        if self.jiwer:
            try:
                return self.jiwer.wer(reference, hypothesis)
            except Exception as e:
                logger.warning(f"Ошибка в jiwer.wer: {e}")

        # Простая реализация WER
        ref_words = reference.split()
        hyp_words = hypothesis.split()

        if len(ref_words) == 0:
            return 1.0 if len(hyp_words) > 0 else 0.0

        # Используем алгоритм Левенштейна для слов
        matrix = self._levenshtein_matrix(ref_words, hyp_words)
        edit_distance = matrix[-1][-1]

        return edit_distance / len(ref_words)

    def _calculate_bleu(self, reference: str, hypothesis: str) -> float:
        """
        Вычисление BLEU Score

        Args:
            reference: Эталонный текст
            hypothesis: Транскрибированный текст

        Returns:
            float: BLEU значение
        """
        if self.nltk:
            try:
                from nltk.translate.bleu_score import sentence_bleu

                ref_tokens = reference.split()
                hyp_tokens = hypothesis.split()

                if len(hyp_tokens) == 0:
                    return 0.0

                # BLEU score с равными весами для 1-4 грамм
                return sentence_bleu([ref_tokens], hyp_tokens)

            except Exception as e:
                logger.warning(f"Ошибка в NLTK BLEU: {e}")

        # Простая реализация на основе n-грамм
        return self._simple_bleu(reference, hypothesis)

    def _simple_bleu(self, reference: str, hypothesis: str, max_n: int = 4) -> float:
        """
        Простая реализация BLEU score

        Args:
            reference: Эталонный текст
            hypothesis: Транскрибированный текст
            max_n: Максимальный размер n-грамм

        Returns:
            float: BLEU значение
        """
        ref_tokens = reference.split()
        hyp_tokens = hypothesis.split()

        if len(hyp_tokens) == 0:
            return 0.0

        # Brevity penalty
        bp = 1.0
        if len(hyp_tokens) < len(ref_tokens):
            bp = np.exp(1 - len(ref_tokens) / len(hyp_tokens))

        # Precision для разных n-грамм
        precisions = []
        for n in range(1, min(max_n + 1, len(hyp_tokens) + 1)):
            ref_ngrams = self._get_ngrams(ref_tokens, n)
            hyp_ngrams = self._get_ngrams(hyp_tokens, n)

            if len(hyp_ngrams) == 0:
                precisions.append(0.0)
                continue

            matches = 0
            for ngram in hyp_ngrams:
                if ngram in ref_ngrams:
                    matches += 1

            precision = matches / len(hyp_ngrams)
            precisions.append(precision)

        if len(precisions) == 0 or any(p == 0 for p in precisions):
            return 0.0

        # Геометрическое среднее
        geo_mean = np.exp(np.mean(np.log(precisions)))

        return bp * geo_mean

    def _get_ngrams(self, tokens: list[str], n: int) -> list[tuple[str, ...]]:
        """Получение n-грамм из токенов"""
        return [tuple(tokens[i:i+n]) for i in range(len(tokens) - n + 1)]

    def _calculate_similarity(self, reference: str, hypothesis: str) -> float:
        """
        Вычисление общей схожести текстов

        Args:
            reference: Эталонный текст
            hypothesis: Транскрибированный текст

        Returns:
            float: Схожесть (0-1)
        """
        # Используем SequenceMatcher из difflib
        matcher = difflib.SequenceMatcher(None, reference, hypothesis)
        return matcher.ratio()

    def _calculate_levenshtein(self, reference: str, hypothesis: str) -> int:
        """
        Вычисление расстояния Левенштейна

        Args:
            reference: Эталонный текст
            hypothesis: Транскрибированный текст

        Returns:
            int: Расстояние Левенштейна
        """
        # Попробуем использовать библиотеку Levenshtein
        try:
            import Levenshtein
            return Levenshtein.distance(reference, hypothesis)
        except ImportError:
            pass

        # Простая реализация
        if len(reference) == 0:
            return len(hypothesis)
        if len(hypothesis) == 0:
            return len(reference)

        matrix = self._levenshtein_matrix(list(reference), list(hypothesis))
        return matrix[-1][-1]

    def _levenshtein_matrix(self, seq1: list, seq2: list) -> list[list[int]]:
        """Создание матрицы для алгоритма Левенштейна"""
        rows = len(seq1) + 1
        cols = len(seq2) + 1

        matrix = [[0] * cols for _ in range(rows)]

        # Инициализация первой строки и столбца
        for i in range(rows):
            matrix[i][0] = i
        for j in range(cols):
            matrix[0][j] = j

        # Заполнение матрицы
        for i in range(1, rows):
            for j in range(1, cols):
                if seq1[i-1] == seq2[j-1]:
                    cost = 0
                else:
                    cost = 1

                matrix[i][j] = min(
                    matrix[i-1][j] + 1,      # deletion
                    matrix[i][j-1] + 1,      # insertion
                    matrix[i-1][j-1] + cost  # substitution
                )

        return matrix

    def _calculate_character_accuracy(self, reference: str, hypothesis: str) -> float:
        """
        Вычисление точности на уровне символов

        Args:
            reference: Эталонный текст
            hypothesis: Транскрибированный текст

        Returns:
            float: Точность символов (0-1)
        """
        if len(reference) == 0:
            return 1.0 if len(hypothesis) == 0 else 0.0

        levenshtein_dist = self._calculate_levenshtein(reference, hypothesis)
        return 1.0 - (levenshtein_dist / len(reference))

    def _analyze_word_errors(self, reference: str, hypothesis: str) -> dict[str, float]:
        """
        Детальный анализ ошибок на уровне слов

        Args:
            reference: Эталонный текст
            hypothesis: Транскрибированный текст

        Returns:
            dict[str, float]: Статистика ошибок
        """
        ref_words = reference.split()
        hyp_words = hypothesis.split()

        if len(ref_words) == 0:
            return {
                "accuracy": 1.0 if len(hyp_words) == 0 else 0.0,
                "insertion_rate": 0.0,
                "deletion_rate": 0.0,
                "substitution_rate": 0.0
            }

        # Создаем матрицу для трассировки операций
        matrix = self._levenshtein_matrix_with_ops(ref_words, hyp_words)

        # Подсчитываем операции
        ops = self._trace_operations(matrix, ref_words, hyp_words)

        substitutions = ops.count('S')
        insertions = ops.count('I')
        deletions = ops.count('D')

        total_words = len(ref_words)

        return {
            "accuracy": 1.0 - (substitutions + insertions + deletions) / total_words,
            "insertion_rate": insertions / total_words,
            "deletion_rate": deletions / total_words,
            "substitution_rate": substitutions / total_words
        }

    def _levenshtein_matrix_with_ops(self, seq1: list, seq2: list) -> list[list[tuple[int, str]]]:
        """Матрица Левенштейна с отслеживанием операций"""
        rows = len(seq1) + 1
        cols = len(seq2) + 1

        matrix = [[(0, '')] * cols for _ in range(rows)]

        # Инициализация
        for i in range(rows):
            matrix[i][0] = (i, 'D')
        for j in range(cols):
            matrix[0][j] = (j, 'I')
        matrix[0][0] = (0, '')

        # Заполнение
        for i in range(1, rows):
            for j in range(1, cols):
                if seq1[i-1] == seq2[j-1]:
                    matrix[i][j] = (matrix[i-1][j-1][0], 'M')  # Match
                else:
                    deletion = matrix[i-1][j][0] + 1
                    insertion = matrix[i][j-1][0] + 1
                    substitution = matrix[i-1][j-1][0] + 1

                    min_cost = min(deletion, insertion, substitution)

                    if min_cost == deletion:
                        matrix[i][j] = (deletion, 'D')
                    elif min_cost == insertion:
                        matrix[i][j] = (insertion, 'I')
                    else:
                        matrix[i][j] = (substitution, 'S')

        return matrix

    def _trace_operations(self, matrix: list[list[tuple[int, str]]], 
                         seq1: list, seq2: list) -> list[str]:
        """Трассировка операций из матрицы"""
        operations = []
        i, j = len(seq1), len(seq2)

        while i > 0 or j > 0:
            op = matrix[i][j][1]
            operations.append(op)

            if op in ['M', 'S']:
                i -= 1
                j -= 1
            elif op == 'D':
                i -= 1
            elif op == 'I':
                j -= 1

        return operations[::-1]

    def _create_html_diff(self, reference: str, hypothesis: str) -> str:
        """
        Создание HTML-представления различий

        Args:
            reference: Эталонный текст
            hypothesis: Транскрибированный текст

        Returns:
            str: HTML с подсвеченными различиями
        """
        differ = difflib.HtmlDiff()
        return differ.make_table(
            reference.splitlines(),
            hypothesis.splitlines(),
            fromdesc="Эталонный текст",
            todesc="Транскрибация"
        )

    def _create_aligned_text(self, reference: str, hypothesis: str) -> str:
        """
        Создание выровненного текста для сравнения

        Args:
            reference: Эталонный текст
            hypothesis: Транскрибированный текст

        Returns:
            str: Выровненный текст
        """
        ref_words = reference.split()
        hyp_words = hypothesis.split()

        # Простое выравнивание на основе SequenceMatcher
        matcher = difflib.SequenceMatcher(None, ref_words, hyp_words)

        result_lines = []
        ref_line = "REF: "
        hyp_line = "HYP: "

        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            if tag == 'equal':
                for k in range(i2 - i1):
                    word = ref_words[i1 + k]
                    ref_line += f"{word} "
                    hyp_line += f"{word} "
            elif tag == 'replace':
                ref_part = " ".join(ref_words[i1:i2])
                hyp_part = " ".join(hyp_words[j1:j2])
                max_len = max(len(ref_part), len(hyp_part))
                ref_line += f"[{ref_part}]".ljust(max_len + 2) + " "
                hyp_line += f"[{hyp_part}]".ljust(max_len + 2) + " "
            elif tag == 'delete':
                deleted = " ".join(ref_words[i1:i2])
                ref_line += f"[{deleted}] "
                hyp_line += "[-] "
            elif tag == 'insert':
                inserted = " ".join(hyp_words[j1:j2])
                ref_line += "[+] "
                hyp_line += f"[{inserted}] "

        return f"{ref_line}\n{hyp_line}"


class ComparisonVisualizer:
    """Визуализация результатов сравнения"""

    @staticmethod
    def create_metrics_summary(result: ComparisonResult) -> dict[str, Any]:
        """
        Создание сводки метрик для отображения

        Args:
            result: Результат сравнения

        Returns:
            dict[str, Any]: Сводка метрик
        """
        return {
            "overall_metrics": {
                "WER": f"{result.wer:.2%}",
                "BLEU Score": f"{result.bleu_score:.3f}",
                "Similarity": f"{result.similarity_score:.2%}",
                "Character Accuracy": f"{result.character_accuracy:.2%}"
            },
            "error_breakdown": {
                "Word Accuracy": f"{result.word_accuracy:.2%}",
                "Substitutions": f"{result.substitution_rate:.2%}",
                "Insertions": f"{result.insertion_rate:.2%}",
                "Deletions": f"{result.deletion_rate:.2%}"
            },
            "text_statistics": {
                "Reference Words": result.reference_word_count,
                "Hypothesis Words": result.hypothesis_word_count,
                "Reference Characters": result.reference_char_count,
                "Hypothesis Characters": result.hypothesis_char_count,
                "Levenshtein Distance": result.levenshtein_distance
            }
        }
