import logging
import numpy as np
import torch
import librosa
from .base_transcriber import BaseTranscriber, TranscriptionResult

logger = logging.getLogger(__name__)

# Настройки аудио от T-one
TARGET_SAMPLE_RATE = 8000
CHUNK_DURATION_MS = 300
EXPECTED_CHUNK_SIZE = TARGET_SAMPLE_RATE * CHUNK_DURATION_MS // 1000


class ToneTranscriber(BaseTranscriber):
    """
    Транскрибатор T-one.
    https://github.com/voicekit-team/T-one
    """

    def __init__(
        self,
        model_name="tone",
        language="ru",
        **kwargs,
    ):
        super().__init__(
            name="T-one",
            model_name=model_name,
            language=language,
            description="Транскрибатор на основе T-one.",
            params=kwargs,
        )
        self.pipeline = None

    def initialize(self) -> bool:
        """Инициализация модели T-one."""
        if self.is_initialized:
            return True
        try:
            from tone import StreamingCTCPipeline

            self.pipeline = StreamingCTCPipeline.from_hugging_face()
            self.is_initialized = True
            logger.info("Модель T-one успешно инициализирована.")
            return True
        except ImportError:
            logger.error(
                "Модуль 'tone' не найден. Пожалуйста, установите его: pip install tone"
            )
            return False
        except Exception as e:
            logger.error(f"Ошибка при инициализации T-one: {e}")
            return False

    def transcribe(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> TranscriptionResult:
        """
        Транскрибация аудио с помощью T-one.
        """
        if not self.is_initialized:
            raise RuntimeError("Модель T-one не инициализирована.")

        # 1. Предобработка аудио
        audio_data, _ = self.preprocess_audio(audio_data, sample_rate)

        # 2. Транскрибация
        all_transcripts = []
        state = None

        for start_idx in range(0, len(audio_data), EXPECTED_CHUNK_SIZE):
            end_idx = start_idx + EXPECTED_CHUNK_SIZE
            chunk = audio_data[start_idx:end_idx]

            if len(chunk) < EXPECTED_CHUNK_SIZE:
                padding = np.zeros(EXPECTED_CHUNK_SIZE - len(chunk), dtype=chunk.dtype)
                chunk = np.concatenate([chunk, padding])

            chunk_int16 = (chunk * 32767).astype(np.int16)
            chunk_int32 = chunk_int16.astype(np.int32)

            new_phrases, state = self.pipeline.forward(chunk_int32, state)
            all_transcripts.extend(new_phrases)

        final_phrases, _ = self.pipeline.finalize(state)
        all_transcripts.extend(final_phrases)

        # 3. Форматирование результата
        full_text = " ".join([phrase.text for phrase in all_transcripts])
        segments = [
            {
                "start": phrase.start_time,
                "end": phrase.end_time,
                "text": phrase.text,
            }
for phrase in all_transcripts
        ]

        return TranscriptionResult(text=full_text, segments=segments, language=self.language)

    def preprocess_audio(self, audio_data: np.ndarray, sample_rate: int) -> tuple:
        """
        Передискретизация аудио до 8000 Гц, как того требует T-one.
        """
        if sample_rate != TARGET_SAMPLE_RATE:
            audio_data = librosa.resample(
                y=audio_data, orig_sr=sample_rate, target_sr=TARGET_SAMPLE_RATE
            )
            logger.info(f"Аудио передискретизировано с {sample_rate} Гц до {TARGET_SAMPLE_RATE} Гц.")

        # Вызов базовой предобработки (нормализация и т.д.)
        return super().preprocess_audio(audio_data, TARGET_SAMPLE_RATE)
