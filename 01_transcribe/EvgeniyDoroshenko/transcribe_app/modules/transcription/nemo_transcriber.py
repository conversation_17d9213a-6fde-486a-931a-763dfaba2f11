import logging
import os
import tempfile
from pathlib import Path

import numpy as np
from dotenv import load_dotenv

load_dotenv()

from .base_transcriber import BaseTranscriber, TranscriptionResult

HUGGINGFACE_TOKEN = os.getenv("HF_TOKEN")

logger = logging.getLogger(__name__)


class NeMoTranscriber(BaseTranscriber):
    """Транскрибатор NVIDIA NeMo"""

    def __init__(
        self,
        model_name: str = "nvidia/stt_ru_fastconformer_hybrid_large_pc",
        language: str = "ru",
        decoding_strategy: str
        | None = None,  # 'greedy' | 'beam_search' | 'beam_search_lm'
        beam_size: int | None = None,
        lm_path: str
        | None = None,  # путь к ARPA LM (если shallow fusion поддерживается)
        lm_alpha: float | None = None,
        lm_beta: float | None = None,
        ctc_weight: float | None = None,  # 0..1 для гибридных моделей
        chunk_size_sec: float | None = None,  # разбиение на чанки (сек)
        vad_rms_threshold: float | None = None,  # простой VAD по энергии (0..1)
        target_sample_rate: int = 16000,
    ) -> None:
        super().__init__(
            name="NeMo ASR",
            model_name=model_name,
            language=language,
            description="NVIDIA NeMo state-of-the-art ASR models",
            params={
                "decoding_strategy": decoding_strategy,
                "beam_size": beam_size,
                "lm_path": lm_path,
                "lm_alpha": lm_alpha,
                "lm_beta": lm_beta,
                "ctc_weight": ctc_weight,
                "chunk_size_sec": chunk_size_sec,
                "vad_rms_threshold": vad_rms_threshold,
                "target_sample_rate": target_sample_rate,
            },
        )
        self.asr_model = None

    def _try_disable_cuda_graphs(self) -> None:
        """Отключает CUDA Graphs в RNNT-декодере, чтобы не требовать cuda-python.
        Безопасно: при отсутствии нужных атрибутов – просто ничего не делает.
        """
        try:
            dec = getattr(self.asr_model, "decoding", None)
            if dec is None:
                return
            for attr_name in dir(dec):
                try:
                    obj = getattr(dec, attr_name)
                except Exception:
                    continue
                if hasattr(obj, "disable_cuda_graphs"):
                    try:
                        obj.disable_cuda_graphs()
                    except Exception:
                        pass
            if hasattr(dec, "allow_cuda_graphs"):
                try:
                    setattr(dec, "allow_cuda_graphs", False)
                except Exception:
                    pass
        except Exception:
            pass

    def _apply_decoding_params(self) -> None:
        """Пробует применить параметры декодирования к модели (безопасно).
        Поддерживается, если у модели есть change_decoding_strategy/decoding.
        """
        try:
            ds = self.params.get("decoding_strategy")
            beam_size = self.params.get("beam_size")
            lm_path = self.params.get("lm_path")
            lm_alpha = self.params.get("lm_alpha")
            lm_beta = self.params.get("lm_beta")
            ctc_weight = self.params.get("ctc_weight")

            # CTC weight для гибридных моделей (если доступно)
            try:
                if ctc_weight is not None and hasattr(self.asr_model, "ctc_weight"):
                    setattr(self.asr_model, "ctc_weight", float(ctc_weight))
            except Exception:
                pass

            # Настройка стратегии декодирования
            change_dec = getattr(self.asr_model, "change_decoding_strategy", None)
            if change_dec is not None:
                cfg: dict = {}
                if ds:
                    cfg["strategy"] = ds
                if beam_size:
                    cfg["beam_size"] = int(beam_size)
                # shallow fusion (если поддерживается моделью)
                if ds == "beam_search_lm":
                    if lm_alpha is not None:
                        cfg["alpha"] = float(lm_alpha)
                    if lm_beta is not None:
                        cfg["beta"] = float(lm_beta)
                    # LM путь может требовать отдельной загрузки в некоторых версиях
                    # Пытаемся передать напрямую, если поле существует
                    if lm_path:
                        cfg["lm_path"] = lm_path
                try:
                    change_dec(cfg)
                except Exception:
                    # fallback: прямое редактирование полей decoding, если есть
                    dec = getattr(self.asr_model, "decoding", None)
                    if dec is not None:
                        if ds and hasattr(dec, "strategy"):
                            try:
                                setattr(dec, "strategy", ds)
                            except Exception:
                                pass
                        if beam_size and hasattr(dec, "beam_size"):
                            try:
                                setattr(dec, "beam_size", int(beam_size))
                            except Exception:
                                pass
                        if ds == "beam_search_lm":
                            for key, val in {
                                "alpha": lm_alpha,
                                "beta": lm_beta,
                            }.items():
                                if val is not None and hasattr(dec, key):
                                    try:
                                        setattr(dec, key, float(val))
                                    except Exception:
                                        pass
        except Exception:
            # Тихо продолжаем с настройками по умолчанию
            pass

    def _postprocess_text_optional(self, text: str) -> str:
        """Добавляет пунктуацию/регистры и ITN, если пакеты установлены (безопасно)."""
        out = text
        # Пунктуация и капитализация (если есть модель)
        try:
            import nemo.collections.nlp as nemo_nlp  # noqa: WPS433

            # Пробуем грузить русскую пунктуацию/капитализацию (если доступна)
            punct_model_name = "punctuation_ru_bert"
            try:
                punct_model = (
                    nemo_nlp.models.PunctuationCapitalizationModel.from_pretrained(
                        punct_model_name
                    )
                )
                out = punct_model.add_punctuation_capitalization([out])[0]
            except Exception:
                # если нет русской — не падаем
                pass
        except Exception:
            pass

        # Инверсная текстовая нормализация (ITN) — если установлена библиотека
        try:
            from nemo_text_processing.inverse_text_normalization.run_predict import (
                InverseNormalizer,  # noqa: WPS433
            )

            itn = InverseNormalizer(lang="ru")
            out = itn.normalize(out)
        except Exception:
            pass
        return out

    def initialize(self) -> bool:
        try:
            import nemo.collections.asr as nemo_asr  # noqa: WPS433

            self.asr_model = nemo_asr.models.ASRModel.from_pretrained(self.model_name)
            # Отключаем CUDA Graphs сразу после загрузки модели
            self._try_disable_cuda_graphs()
            # Применяем параметры декодирования (beam/LM/ctc_weight), если заданы
            self._apply_decoding_params()

            self.is_initialized = True
            logger.info("NeMo model %s loaded", self.model_name)
            return True
        except Exception as exc:
            logger.error("NeMo init failed: %s", exc)
            return False

    def transcribe(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> TranscriptionResult:
        if not self.is_initialized and not self.initialize():
            return TranscriptionResult(text="")

        import soundfile as sf  # noqa: WPS433

        # 1) Приводим сэмплинг к целевому (по умолчанию 16 кГц)
        sr = int(sample_rate)
        target_sr = int(self.params.get("target_sample_rate") or 16000)
        audio = audio_data
        if sr != target_sr:
            try:
                import librosa  # noqa: WPS433

                audio = librosa.resample(audio.astype(np.float32), orig_sr=sr, target_sr=target_sr)
                sr = target_sr
            except Exception:
                # если нет librosa – используем как есть
                audio = audio_data
                sr = sample_rate

        chunk_sec = self.params.get("chunk_size_sec") or 0
        vad_thr = self.params.get("vad_rms_threshold")

        # 2) Если задан chunking – режем на куски и батчим
        if chunk_sec and chunk_sec > 0:
            chunk_len = int(chunk_sec * sr)
            tmp_paths: list[Path] = []
            offsets_sec: list[float] = []
            try:
                total_len = len(audio)
                pos = 0
                while pos < total_len:
                    end = min(pos + chunk_len, total_len)
                    chunk = audio[pos:end]
                    # простой RMS-VAD (опционально)
                    use_chunk = True
                    if vad_thr is not None:
                        rms = float(np.sqrt(np.mean((chunk.astype(np.float32)) ** 2)) + 1e-8)
                        use_chunk = rms >= float(vad_thr)
                    if use_chunk and len(chunk) > int(0.1 * sr):  # отбрасываем очень короткие
                        with tempfile.NamedTemporaryFile("wb", suffix=".wav", delete=False) as tmp:
                            sf.write(tmp, chunk, sr)
                            tmp_paths.append(Path(tmp.name))
                            offsets_sec.append(pos / sr)
                    pos = end

                segments: list[dict] = []
                full_text_parts: list[str] = []

                if tmp_paths:
                    # декодируем пачкой
                    try:
                        self._try_disable_cuda_graphs()
                        hyps = None
                        try:
                            hyps = self.asr_model.transcribe(
                                [str(p) for p in tmp_paths], return_hypotheses=True
                            )
                        except TypeError:
                            hyps = self.asr_model.transcribe([str(p) for p in tmp_paths])

                        for hyp, off in zip(hyps or [], offsets_sec):
                            t = (
                                getattr(hyp, "text", None)
                                or (hyp[0] if isinstance(hyp, (list, tuple)) and hyp else "")
                                or str(hyp)
                            )
                            full_text_parts.append((t or "").strip())
                            words = getattr(hyp, "words", None)
                            if words:
                                gap_threshold = 0.8
                                seg_start = None
                                seg_text_parts: list[str] = []
                                last_end = None
                                for w in words:
                                    w_start = off + float(getattr(w, "start_time", 0.0))
                                    w_end = off + float(getattr(w, "end_time", 0.0))
                                    w_word = getattr(w, "word", "")
                                    if seg_start is None:
                                        seg_start = w_start
                                        seg_text_parts = [w_word]
                                    else:
                                        gap = w_start - (last_end if last_end is not None else w_start)
                                        if gap > gap_threshold and seg_text_parts:
                                            segments.append(
                                                {
                                                    "start": seg_start,
                                                    "end": float(last_end) if last_end is not None else seg_start,
                                                    "text": " ".join(seg_text_parts).strip(),
                                                }
                                            )
                                            seg_start = w_start
                                            seg_text_parts = [w_word]
                                        else:
                                            seg_text_parts.append(w_word)
                                    last_end = w_end
                                if seg_text_parts:
                                    segments.append(
                                        {
                                            "start": seg_start if seg_start is not None else off,
                                            "end": float(last_end) if last_end is not None else (seg_start or off),
                                            "text": " ".join(seg_text_parts).strip(),
                                        }
                                    )

                    finally:
                        for p in tmp_paths:
                            try:
                                os.remove(p)
                            except Exception:
                                pass

                final_text = " ".join([p for p in full_text_parts if p]).strip()
                final_text = self._postprocess_text_optional(final_text)
                return TranscriptionResult(
                    text=final_text, language=self.language, segments=segments or None
                )

        # 3) Обычный путь (без chunking)
        with tempfile.NamedTemporaryFile("wb", suffix=".wav", delete=False) as tmp:
            sf.write(tmp, audio, sr)
            tmp_path = Path(tmp.name)

        try:
            segments: list[dict] = []
            text = ""
            hyps = None
            try:
                self._try_disable_cuda_graphs()
                hyps = self.asr_model.transcribe([str(tmp_path)], return_hypotheses=True)
            except TypeError:
                hyps = self.asr_model.transcribe([str(tmp_path)])

            if hyps:
                hyp0 = hyps[0]
                text = (
                    getattr(hyp0, "text", None)
                    or (hyp0[0] if isinstance(hyp0, (list, tuple)) and hyp0 else "")
                    or str(hyp0)
                )
                words = getattr(hyp0, "words", None)
                if words:
                    gap_threshold = 0.8
                    seg_start = None
                    seg_text_parts: list[str] = []
                    last_end = None
                    for w in words:
                        w_start = float(getattr(w, "start_time", 0.0))
                        w_end = float(getattr(w, "end_time", w_start))
                        w_word = getattr(w, "word", "")
                        if seg_start is None:
                            seg_start = w_start
                            seg_text_parts = [w_word]
                        else:
                            gap = w_start - (last_end if last_end is not None else w_start)
                            if gap > gap_threshold and seg_text_parts:
                                segments.append(
                                    {
                                        "start": seg_start,
                                        "end": float(last_end) if last_end is not None else seg_start,
                                        "text": " ".join(seg_text_parts).strip(),
                                    }
                                )
                                seg_start = w_start
                                seg_text_parts = [w_word]
                            else:
                                seg_text_parts.append(w_word)
                        last_end = w_end
                    if seg_text_parts:
                        segments.append(
                            {
                                "start": seg_start if seg_start is not None else 0.0,
                                "end": float(last_end) if last_end is not None else (seg_start or 0.0),
                                "text": " ".join(seg_text_parts).strip(),
                            }
                        )

            final_text = self._postprocess_text_optional((text or "").strip())
            return TranscriptionResult(
                text=final_text, language=self.language, segments=segments or None
            )
        except Exception as exc:
            logger.error("NeMo transcription failed: %s", exc)
            return TranscriptionResult(text="")
        finally:
            try:
                os.remove(tmp_path)
            except Exception:
                pass

    def transcribe_many(
        self, items: list[tuple[np.ndarray, int]]
    ) -> list[TranscriptionResult]:
        """Батчевая транскрибация списка (audio, sr) для NeMo.
        Использует возможность asr_model.transcribe по списку путей.
        """
        results: list[TranscriptionResult] = []
        if not self.is_initialized and not self.initialize():
            return [TranscriptionResult(text="") for _ in items]

        import soundfile as sf  # noqa: WPS433

        # Сохраняем во временные файлы
        tmp_paths: list[Path] = []
        try:
            target_sr = int(self.params.get("target_sample_rate") or 16000)
            for audio_data, sample_rate in items:
                sr = int(sample_rate)
                audio = audio_data
                if sr != target_sr:
                    try:
                        import librosa  # noqa: WPS433
                        audio = librosa.resample(audio.astype(np.float32), orig_sr=sr, target_sr=target_sr)
                        sr = target_sr
                    except Exception:
                        audio = audio_data
                        sr = sample_rate
                with tempfile.NamedTemporaryFile("wb", suffix=".wav", delete=False) as tmp:
                    sf.write(tmp, audio, sr)
                    tmp_paths.append(Path(tmp.name))

            try:
                # Гарантированно отключаем CUDA Graphs
                self._try_disable_cuda_graphs()
                hyps = None
                try:
                    hyps = self.asr_model.transcribe(
                        [str(p) for p in tmp_paths], return_hypotheses=True
                    )
                except TypeError:
                    hyps = self.asr_model.transcribe([str(p) for p in tmp_paths])

                # Преобразуем гипотезы в наши результаты
                for hyp in hyps or []:
                    text = (
                        getattr(hyp, "text", None)
                        or (hyp[0] if isinstance(hyp, (list, tuple)) and hyp else "")
                        or str(hyp)
                    )
                    segments: list[dict] | None = None
                    words = getattr(hyp, "words", None)
                    if words:
                        gap_threshold = 0.8
                        segs: list[dict] = []
                        seg_start = None
                        seg_text_parts: list[str] = []
                        last_end = None
                        for w in words:
                            w_start = float(getattr(w, "start_time", 0.0))
                            w_end = float(getattr(w, "end_time", w_start))
                            w_word = getattr(w, "word", "")
                            if seg_start is None:
                                seg_start = w_start
                                seg_text_parts = [w_word]
                            else:
                                gap = w_start - (
                                    last_end if last_end is not None else w_start
                                )
                                if gap > gap_threshold and seg_text_parts:
                                    segs.append(
                                        {
                                            "start": seg_start,
                                            "end": float(last_end)
                                            if last_end is not None
                                            else seg_start,
                                            "text": " ".join(seg_text_parts).strip(),
                                        }
                                    )
                                    seg_start = w_start
                                    seg_text_parts = [w_word]
                                else:
                                    seg_text_parts.append(w_word)
                            last_end = w_end
                        if seg_text_parts:
                            segs.append(
                                {
                                    "start": seg_start
                                    if seg_start is not None
                                    else 0.0,
                                    "end": float(last_end)
                                    if last_end is not None
                                    else (seg_start or 0.0),
                                    "text": " ".join(seg_text_parts).strip(),
                                }
                            )
                        segments = segs or None
                    final_text = self._postprocess_text_optional((text or "").strip())
                    results.append(
                        TranscriptionResult(
                            text=final_text,
                            language=self.language,
                            segments=segments,
                        )
                    )

                # Если hyps пустой/None, вернем пустые результаты соответствующего размера
                if not hyps:
                    for _ in items:
                        results.append(TranscriptionResult(text=""))

            finally:
                # Чистим временные файлы
                for p in tmp_paths:
                    try:
                        os.remove(p)
                    except Exception:
                        pass
        except Exception as exc:
            logger.error("NeMo batch transcription failed: %s", exc)
            # Фоллбэк: пустые результаты
            return [TranscriptionResult(text="") for _ in items]

        return results
