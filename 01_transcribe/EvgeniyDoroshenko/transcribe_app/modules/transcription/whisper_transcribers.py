import logging
import os
import tempfile
from pathlib import Path
from typing import List, Tuple

import numpy as np

from .base_transcriber import BaseTranscriber, TranscriptionResult

logger = logging.getLogger(__name__)


class WhisperXTranscriber(BaseTranscriber):
    """Транскрибатор WhisperX"""

    def __init__(
        self,
        model_name: str = "large-v2",
        language: str = "ru",
        batch_size: int = 16,
        compute_type: str = "auto",
        diarize: bool = False,
    ) -> None:
        super().__init__(
            name="WhisperX",
            model_name=model_name,
            language=language,
            description="Fast transcription with word-level timestamps and alignment",
            params={
                "batch_size": batch_size,
                "compute_type": compute_type,
                "diarize": diarize,
            },
        )

        self.whisperx = None
        self.model = None
        # Кэш для моделей выравнивания, ключ = language_code
        self._align_cache: dict[str, tuple[object, dict]] = {}

    def initialize(self) -> bool:  # noqa: D401
        """Lazy-load WhisperX model."""
        try:
            import torch  # noqa: WPS433
            import whisperx  # noqa: WPS433

            device = "cuda" if torch.cuda.is_available() else "cpu"
            compute_type = self.params["compute_type"]
            if compute_type == "auto":
                compute_type = "float16" if device == "cuda" else "int8"

            self.model = whisperx.load_model(
                self.model_name,
                device=device,
                compute_type=compute_type,
            )
            self.whisperx = whisperx
            self.is_initialized = True
            logger.info(
                "WhisperX initialised (%s) on %s with compute_type=%s",
                self.model_name,
                device,
                compute_type,
            )
            return True
        except Exception as exc:  # noqa: WPS430
            logger.error("WhisperX init failed: %s", exc)
            return False

    def transcribe(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> TranscriptionResult:
        """Выполняет транскрибацию через WhisperX."""
        if not self.is_initialized and not self.initialize():
            return TranscriptionResult(text="")

        import soundfile as sf
        import torch

        with tempfile.NamedTemporaryFile("wb", suffix=".wav", delete=False) as tmp:
            sf.write(tmp, audio_data, sample_rate)
            tmp_path = Path(tmp.name)

        try:
            audio = self.whisperx.load_audio(str(tmp_path))

            hm = self.model.transcribe(
                audio,
                language=self.language if self.language != "auto" else None,
                batch_size=self.params["batch_size"],
            )

            device = "cuda" if torch.cuda.is_available() else "cpu"

            align_model, meta = self.whisperx.load_align_model(
                language_code=hm["language"],
                device=device,  # Используем определенное выше устройство
            )

            aligned = self.whisperx.align(
                hm["segments"],
                align_model,
                meta,
                audio,
                device=device,
                return_char_alignments=False,
            )

            segments = aligned["segments"]
            text_full = " ".join(s["text"].strip() for s in segments).strip()

            seg_out = [
                {
                    "start": s["start"],
                    "end": s["end"],
                    "text": s["text"].strip(),
                }
                for s in segments
            ]

            return TranscriptionResult(
                text=text_full,
                language=hm.get("language", self.language),
                segments=seg_out,
            )
        except Exception as exc:
            logger.error("Ошибка транскрибации WhisperX: %s", exc, exc_info=True)
            return TranscriptionResult(text="")
        finally:
            try:
                os.remove(tmp_path)
            except Exception:
                pass

    def transcribe_many(
        self, items: List[Tuple[np.ndarray, int]]
    ) -> List[TranscriptionResult]:
        """
        Батчевая обработка для WhisperX: держим модель загруженной и переиспользуем
        align-модель по языковому коду. Возвращает результаты в исходном порядке.
        """
        if not self.is_initialized and not self.initialize():
            return [TranscriptionResult(text="") for _ in items]

        import soundfile as sf
        import torch

        results: List[TranscriptionResult] = []
        tmp_paths: list[Path] = []
        try:
            # Готовим временные файлы
            for audio_data, sample_rate in items:
                with tempfile.NamedTemporaryFile(
                    "wb", suffix=".wav", delete=False
                ) as tmp:
                    sf.write(tmp, audio_data, sample_rate)
                    tmp_paths.append(Path(tmp.name))

            device = "cuda" if torch.cuda.is_available() else "cpu"

            for p in tmp_paths:
                try:
                    audio = self.whisperx.load_audio(str(p))
                    hm = self.model.transcribe(
                        audio,
                        language=self.language if self.language != "auto" else None,
                        batch_size=self.params["batch_size"],
                    )
                    lang_code = hm.get("language", self.language)
                    # align-модель из кэша
                    if lang_code not in self._align_cache:
                        align_model, meta = self.whisperx.load_align_model(
                            language_code=lang_code, device=device
                        )
                        self._align_cache[lang_code] = (align_model, meta)
                    else:
                        align_model, meta = self._align_cache[lang_code]

                    aligned = self.whisperx.align(
                        hm["segments"],
                        align_model,
                        meta,
                        audio,
                        device=device,
                        return_char_alignments=False,
                    )
                    segments = aligned["segments"]
                    text_full = " ".join(s["text"].strip() for s in segments).strip()
                    seg_out = [
                        {
                            "start": s["start"],
                            "end": s["end"],
                            "text": s["text"].strip(),
                        }
                        for s in segments
                    ]
                    results.append(
                        TranscriptionResult(
                            text=text_full, language=lang_code, segments=seg_out
                        )
                    )
                except Exception as exc:
                    logger.error("WhisperX batch item failed: %s", exc)
                    results.append(TranscriptionResult(text=""))
        finally:
            for p in tmp_paths:
                try:
                    os.remove(p)
                except Exception:
                    pass

        return results


class FasterWhisperTranscriber(BaseTranscriber):
    """Транскрибатор faster-whisper"""

    def __init__(
        self,
        model_size: str = "large-v2",
        device: str = "auto",
        compute_type: str = "float16",
        language: str = "ru",
    ):
        super().__init__(
            name="Faster Whisper",
            model_name=model_size,
            language=language,
            description="Оптимизированная версия Whisper (до 4x быстрее)",
            params={"device": device, "compute_type": compute_type},
        )
        self.model_size = model_size

    def initialize(self) -> bool:
        """Инициализация faster-whisper"""
        try:
            from faster_whisper import WhisperModel

            # Определяем устройство
            device = self.params["device"]
            if device == "auto":
                import torch

                device = "cuda" if torch.cuda.is_available() else "cpu"

            # Корректируем compute_type для CPU
            compute_type = self.params["compute_type"]
            if device == "cpu" and compute_type == "float16":
                compute_type = "int8"

            self.model = WhisperModel(
                self.model_size, device=device, compute_type=compute_type
            )

            self.is_initialized = True
            logger.info(
                f"Faster Whisper инициализирован: {self.model_size} на {device}"
            )
            return True

        except ImportError as e:
            logger.error(f"Не удалось импортировать faster-whisper: {e}")
            return False
        except Exception as e:
            logger.error(f"Ошибка инициализации faster-whisper: {e}")
            return False

    def transcribe(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> TranscriptionResult:
        """
        Транскрибация с помощью faster-whisper

        Args:
            audio_data: Аудиоданные
            sample_rate: Частота дискретизации

        Returns:
            TranscriptionResult: Результат транскрибации
        """
        try:
            # faster-whisper работает с файлами, создаем временный файл
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                import soundfile as sf

                sf.write(temp_file.name, audio_data, sample_rate)
                temp_file_path = temp_file.name

            try:
                # Транскрибация
                segments, info = self.model.transcribe(
                    temp_file_path,
                    language=self.language if self.language != "auto" else None,
                    beam_size=5,
                    word_timestamps=True,
                )

                # Собираем результат
                segments_list = list(segments)
                text_parts = []
                segment_info = []

                for segment in segments_list:
                    text_parts.append(segment.text)
                    segment_info.append(
                        {
                            "start": segment.start,
                            "end": segment.end,
                            "text": segment.text,
                        }
                    )

                full_text = " ".join(text_parts).strip()

                result = TranscriptionResult(
                    text=full_text,
                    language=getattr(info, "language", self.language),
                    segments=segment_info,
                )

                logger.info(
                    f"Faster Whisper: транскрибировано {len(full_text)} символов"
                )
                return result

            finally:
                # Удаляем временный файл
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)

        except Exception as e:
            logger.error(f"Ошибка в Faster Whisper: {e}")
            return TranscriptionResult(text="")

    def transcribe_many(
        self, items: List[Tuple[np.ndarray, int]]
    ) -> List[TranscriptionResult]:
        """
        Батчевая обработка для faster-whisper: переиспользуем загруженную модель
        и обрабатываем список аудио-файлов последовательно без переинициализации.
        """
        if not self.is_initialized and not self.initialize():
            return [TranscriptionResult(text="") for _ in items]

        import soundfile as sf

        results: List[TranscriptionResult] = []
        tmp_files: list[str] = []
        try:
            # Подготовим временные wav для всех элементов
            for audio_data, sample_rate in items:
                with tempfile.NamedTemporaryFile(
                    suffix=".wav", delete=False
                ) as temp_file:
                    sf.write(temp_file.name, audio_data, sample_rate)
                    tmp_files.append(temp_file.name)

            # Теперь транскрибируем по очереди, не выгружая модель
            for path in tmp_files:
                try:
                    segments, info = self.model.transcribe(
                        path,
                        language=self.language if self.language != "auto" else None,
                        beam_size=5,
                        word_timestamps=True,
                    )
                    segments_list = list(segments)
                    text_parts = []
                    segment_info = []
                    for segment in segments_list:
                        text_parts.append(segment.text)
                        segment_info.append(
                            {
                                "start": segment.start,
                                "end": segment.end,
                                "text": segment.text,
                            }
                        )
                    full_text = " ".join(text_parts).strip()
                    results.append(
                        TranscriptionResult(
                            text=full_text,
                            language=getattr(info, "language", self.language),
                            segments=segment_info,
                        )
                    )
                except Exception as exc:
                    logger.error("Faster Whisper batch item failed: %s", exc)
                    results.append(TranscriptionResult(text=""))
        finally:
            for fp in tmp_files:
                try:
                    if os.path.exists(fp):
                        os.unlink(fp)
                except Exception:
                    pass

        return results


class OpenAIWhisperTranscriber(BaseTranscriber):
    """Транскрибатор OpenAI Whisper"""

    def __init__(self, model_size: str = "large-v2", language: str = "ru"):
        super().__init__(
            name="OpenAI Whisper",
            model_name=model_size,
            language=language,
            description="Оригинальная модель Whisper от OpenAI",
            params={},
        )
        self.model_size = model_size

    def initialize(self) -> bool:
        """Инициализация OpenAI Whisper"""
        try:
            import whisper

            self.model = whisper.load_model(self.model_size)
            self.whisper = whisper
            self.is_initialized = True
            logger.info(f"OpenAI Whisper инициализирован: {self.model_size}")
            return True

        except ImportError as e:
            logger.error(f"Не удалось импортировать whisper: {e}")
            return False
        except Exception as e:
            logger.error(f"Ошибка инициализации OpenAI Whisper: {e}")
            return False

    def transcribe(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> TranscriptionResult:
        """
        Транскрибация с помощью OpenAI Whisper

        Args:
            audio_data: Аудиоданные
            sample_rate: Частота дискретизации

        Returns:
            TranscriptionResult: Результат транскрибации
        """
        try:
            # Whisper ожидает 16kHz
            if sample_rate != 16000:
                import librosa

                audio_data = librosa.resample(
                    audio_data, orig_sr=sample_rate, target_sr=16000
                )

            # Транскрибация
            result = self.model.transcribe(
                audio_data,
                language=self.language if self.language != "auto" else None,
                word_timestamps=True,
            )

            # Подготавливаем сегменты
            segments = []
            if "segments" in result:
                for segment in result["segments"]:
                    segments.append(
                        {
                            "start": segment.get("start", 0),
                            "end": segment.get("end", 0),
                            "text": segment.get("text", ""),
                        }
                    )

            transcription_result = TranscriptionResult(
                text=result["text"].strip(),
                language=result.get("language", self.language),
                segments=segments,
            )

            logger.info(
                f"OpenAI Whisper: транскрибировано {len(result['text'])} символов"
            )
            return transcription_result

        except Exception as e:
            logger.error(f"Ошибка в OpenAI Whisper: {e}")
            return TranscriptionResult(text="")
