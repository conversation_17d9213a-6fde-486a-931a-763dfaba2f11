import gc
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any

import numpy as np
import torch

logger = logging.getLogger(__name__)


@dataclass
class TranscriptionResult:
    """Результат транскрибации"""

    text: str
    language: str | None = None
    segments: list[dict] | None = None
    processing_time: float | None = None
    model_info: dict[str, Any] | None = None


class BaseTranscriber(ABC):
    """Базовый класс для всех систем транскрибации"""

    def __init__(
        self,
        name: str,
        model_name: str,
        language: str = "ru",
        description: str = "",
        params: dict[str, Any] | None = None,
        auto_cleanup: bool = False,
    ):
        self.name = name
        self.model_name = model_name
        self.language = language
        self.description = description
        self.params = params or {}
        self.is_initialized = False
        self.model = None
        self.auto_cleanup = auto_cleanup

    @abstractmethod
    def initialize(self) -> bool:
        """
        Инициализация модели транскрибации

        Returns:
            bool: True если инициализация успешна
        """
        pass

    @abstractmethod
    def transcribe(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> TranscriptionResult:
        """
        Основной метод транскрибации аудио

        Args:
            audio_data: Входные аудиоданные
            sample_rate: Частота дискретизации

        Returns:
            TranscriptionResult: Результат транскрибации
        """
        pass

    def preprocess_audio(self, audio_data: np.ndarray, sample_rate: int) -> tuple:
        """
        Предварительная обработка аудио для транскрибации

        Args:
            audio_data: Входные аудиоданные
            sample_rate: Частота дискретизации

        Returns:
            tuple: Обработанные аудиоданные и частота дискретизации
        """
        # Базовая проверка входных данных
        if len(audio_data) == 0:
            raise ValueError("Пустые аудиоданные")

        # Нормализация аудио
        if np.max(np.abs(audio_data)) > 0:
            audio_data = audio_data / np.max(np.abs(audio_data)) * 0.9
            logger.debug("Аудио нормализовано для транскрибации")

        # Экономия памяти: используем float32 вместо float64
        if audio_data.dtype != np.float32:
            audio_data = audio_data.astype(np.float32, copy=False)

        return audio_data, sample_rate

    def postprocess_text(self, text: str) -> str:
        """
        Пост-обработка текста транскрибации

        Args:
            text: Исходный текст транскрибации

        Returns:
            str: Обработанный текст
        """
        if not text:
            return ""

        # Базовая очистка текста
        text = text.strip()

        # Удаление множественных пробелов
        import re

        text = re.sub(r"\s+", " ", text)

        logger.debug("Текст транскрибации обработан")
        return text

    def get_info(self) -> dict[str, Any]:
        """
        Возвращает информацию о системе транскрибации

        Returns:
            dict[str, Any]: Информация о системе
        """
        return {
            "name": self.name,
            "model_name": self.model_name,
            "language": self.language,
            "description": self.description,
            "params": self.params,
            "initialized": self.is_initialized,
        }

    def set_params(self, **kwargs) -> None:
        """
        Устанавливает параметры системы транскрибации

        Args:
            **kwargs: Параметры для установки
        """
        self.params.update(kwargs)
        logger.info(f"Обновлены параметры для {self.name}: {kwargs}")

    def get_supported_languages(self) -> list[str]:
        """
        Возвращает список поддерживаемых языков

        Returns:
            list[str]: Список кодов языков
        """
        # Базовая реализация, переопределяется в дочерних классах
        return ["ru"]

    def is_language_supported(self, language: str) -> bool:
        """
        Проверяет поддержку языка

        Args:
            language: Код языка

        Returns:
            bool: True если язык поддерживается
        """
        return language in self.get_supported_languages()

    def cleanup_model(self) -> None:
        """Очистка модели из памяти (переопределяется в дочерних классах)"""
        try:
            if hasattr(self, "model") and self.model is not None:
                # Попытка перенести модель на CPU
                if hasattr(self.model, "cpu"):
                    self.model.cpu()

                # Удаление модели
                del self.model
                self.model = None

            # Сброс флага инициализации
            self.is_initialized = False

            logger.debug(f"Модель {self.name} очищена из памяти")

        except Exception as e:
            logger.warning(f"Ошибка при очистке модели {self.name}: {e}")

    def cleanup_gpu_memory(self) -> None:
        """
        Очистка GPU памяти
        """
        try:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()

            # Принудительная сборка мусора
            gc.collect()

            # Небольшая задержка для полной выгрузки
            import time

            time.sleep(0.5)

        except Exception as e:
            logger.warning(f"Ошибка при очистке GPU памяти: {e}")

    def full_cleanup(self) -> None:
        """
        Полная очистка ресурсов транскрибатора
        """
        self.cleanup_model()
        self.cleanup_gpu_memory()
        logger.info(f"Выполнена полная очистка для {self.name}")

    def transcribe_with_preprocessing(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> TranscriptionResult:
        """
        Полный пайплайн транскрибации с пред- и пост-обработкой
        """
        import time

        start_time = time.time()

        try:
            if not self.is_initialized:
                if not self.initialize():
                    raise RuntimeError(f"Не удалось инициализировать {self.name}")

            # Предобработка
            processed_audio, processed_sr = self.preprocess_audio(
                audio_data, sample_rate
            )

            # Основная транскрибация (без вычисления градиентов)
            try:
                cm = getattr(torch, "inference_mode", torch.no_grad)()
            except Exception:
                cm = torch.no_grad()
            with cm:
                result = self.transcribe(processed_audio, processed_sr)

            # Постобработка текста
            result.text = self.postprocess_text(result.text)

            # Добавляем информацию о времени обработки
            result.processing_time = time.time() - start_time
            result.model_info = self.get_info()

            logger.info(
                f"Транскрибация завершена методом {self.name} за {result.processing_time:.2f}с"
            )
            return result

        except Exception as e:
            logger.error(f"Ошибка в транскрибации {self.name}: {e}")
            raise
        finally:
            # Автоматическая очистка после транскрибации (опционально)
            if hasattr(self, "auto_cleanup") and self.auto_cleanup:
                self.full_cleanup()


class TranscriptionManager:
    """Менеджер для управления несколькими системами транскрибации"""

    def __init__(self):
        self.transcribers = {}
        self.default_transcriber = None

    def register_transcriber(
        self, transcriber: BaseTranscriber, make_default: bool = False
    ) -> None:
        """
        Регистрирует систему транскрибации

        Args:
            transcriber: Экземпляр системы транскрибации
            make_default: Сделать системой по умолчанию
        """
        self.transcribers[transcriber.name] = transcriber

        if make_default or self.default_transcriber is None:
            self.default_transcriber = transcriber.name

        logger.info(f"Зарегистрирована система транскрибации: {transcriber.name}")

    def get_transcriber(self, name: str | None = None) -> BaseTranscriber | None:
        """
        Получает систему транскрибации по имени

        Args:
            name: Имя системы (если None, возвращает систему по умолчанию)

        Returns:
            BaseTranscriber]: Экземпляр системы транскрибации
        """
        if name is None:
            name = self.default_transcriber

        return self.transcribers.get(name)

    def list_transcribers(self) -> list[str]:
        """
        Возвращает список доступных систем транскрибации

        Returns:
            list[str]: Список имен систем
        """
        return list(self.transcribers.keys())

    def transcribe_stereo_channels(
        self,
        left_channel: np.ndarray,
        right_channel: np.ndarray,
        sample_rate: int,
        transcriber_name: str | None = None,
    ) -> str:
        """
        Транскрибация стерео каналов отдельно
        """
        transcriber = self.get_transcriber(transcriber_name)
        if transcriber is None:
            raise ValueError(f"Система транскрибации '{transcriber_name}' не найдена")

        # Транскрибируем левый канал (менеджер)
        left_result = transcriber.transcribe_with_preprocessing(
            left_channel, sample_rate
        )

        # Транскрибируем правый канал (клиент)
        right_result = transcriber.transcribe_with_preprocessing(
            right_channel, sample_rate
        )

        # Объединяем результаты в формате диалога
        merged_text = f"М: {left_result.text}\n\nК: {right_result.text}"

        return merged_text

    def transcribe(
        self,
        audio_data: np.ndarray,
        sample_rate: int,
        transcriber_name: str | None = None,
        cleanup_after: bool = True,
    ) -> TranscriptionResult:
        """
        Выполняет транскрибацию с указанной системой

        Args:
            audio_data: Аудиоданные
            sample_rate: Частота дискретизации
            transcriber_name: Имя системы транскрибации
            cleanup_after: Очищать память после транскрибации

        Returns:
            TranscriptionResult: Результат транскрибации
        """
        transcriber = self.get_transcriber(transcriber_name)
        if transcriber is None:
            raise ValueError(f"Система транскрибации '{transcriber_name}' не найдена")

        try:
            result = transcriber.transcribe_with_preprocessing(audio_data, sample_rate)
            return result
        finally:
            if cleanup_after:
                transcriber.full_cleanup()

    def transcribe_stereo_dialog(
        self,
        left_channel: np.ndarray,
        right_channel: np.ndarray,
        sample_rate: int,
        transcriber_name: str | None = None,
        cleanup_after: bool = True,
    ) -> TranscriptionResult:
        """
        Транскрибация каждого канала отдельно с объединением в диалог.
        Пытаемся интерливинг по временным меткам, если сегменты доступны.
        Иначе — фоллбэк без таймкодов. По умолчанию освобождаем память модели.
        """
        transcriber = self.get_transcriber(transcriber_name)
        if transcriber is None:
            raise ValueError(f"Система транскрибации '{transcriber_name}' не найдена")

        try:
            # Выполняем поочередную транскрибацию каналов на одной загруженной модели
            left_res = transcriber.transcribe_with_preprocessing(
                left_channel, sample_rate
            )
            right_res = transcriber.transcribe_with_preprocessing(
                right_channel, sample_rate
            )

            # Если есть тайминги у обоих — интерливинг
            left_segs = left_res.segments or []
            right_segs = right_res.segments or []
            can_merge_by_time = (
                len(left_segs) > 0
                and len(right_segs) > 0
                and all(isinstance(s, dict) and ("start" in s) for s in left_segs)
                and all(isinstance(s, dict) and ("start" in s) for s in right_segs)
            )

            merged_segments: list[dict] = []
            merged_text: str = ""
            if can_merge_by_time:
                li, ri = 0, 0
                while li < len(left_segs) or ri < len(right_segs):
                    if ri >= len(right_segs) or (
                        li < len(left_segs)
                        and float(left_segs[li].get("start", 0.0))
                        <= float(right_segs[ri].get("start", 0.0))
                    ):
                        seg_src = left_segs[li]
                        li += 1
                    else:
                        seg_src = right_segs[ri]
                        ri += 1
                    seg_text = (seg_src.get("text") or "").strip()
                    if not seg_text:
                        # Пропускаем пустые куски, чтобы не было пустых строк в UI
                        continue
                    seg = {
                        "start": float(seg_src.get("start", 0.0)),
                        "end": float(seg_src.get("end", seg_src.get("start", 0.0))),
                        "text": seg_text,
                    }
                    merged_segments.append(seg)
                if merged_segments:
                    merged_text = "\n".join(
                        [s["text"].strip() for s in merged_segments]
                    )
            if not merged_text:
                # Фоллбек без таймингов или если после фильтрации сегментов не осталось
                parts = [left_res.text.strip(), right_res.text.strip()]
                parts = [p for p in parts if p]
                merged_text = "\n\n".join(parts)
                merged_segments = []

            return TranscriptionResult(
                text=merged_text,
                language=left_res.language or right_res.language,
                segments=merged_segments or None,
            )
        finally:
            if cleanup_after:
                try:
                    transcriber.full_cleanup()
                except Exception:
                    pass

        def transcribe_stereo_dialog_many(
            self,
            items: list[tuple[np.ndarray, np.ndarray, int]],
            transcriber_name: str | None = None,
            cleanup_after: bool = True,
        ) -> list[TranscriptionResult]:
            """
            Батчевая стерео-транскрибация: каждый элемент = (left, right, sr).
            Если транскрибатор поддерживает transcribe_many, используем его для левого и правого
            каналов отдельно, затем объединяем сегменты по времени для каждой пары.
            В противном случае — последовательная обработка без выгрузки модели между элементами.
            """
            transcriber = self.get_transcriber(transcriber_name)
            if transcriber is None:
                raise ValueError(
                    f"Система транскрибации '{transcriber_name}' не найдена"
                )

            results: list[TranscriptionResult] = []
            try:
                has_batch = hasattr(transcriber, "transcribe_many")
                if has_batch:
                    left_items = [(left_ch, sr) for (left_ch, right_ch, sr) in items]
                    right_items = [(right_ch, sr) for (left_ch, right_ch, sr) in items]
                    left_results = transcriber.transcribe_many(left_items)
                    right_results = transcriber.transcribe_many(right_items)
                    pairs = zip(left_results, right_results)
                else:
                    # Фоллбэк: без batch, но не выгружаем модель между элементами
                    pairs = []
                    left_results: list[TranscriptionResult] = []
                    right_results: list[TranscriptionResult] = []
                    for left_ch, right_ch, sr in items:
                        left_results.append(
                            transcriber.transcribe_with_preprocessing(left_ch, sr)
                        )
                        right_results.append(
                            transcriber.transcribe_with_preprocessing(right_ch, sr)
                        )
                    pairs = zip(left_results, right_results)

                for left_res, right_res in pairs:
                    left_segs = left_res.segments or []
                    right_segs = right_res.segments or []
                    can_merge_by_time = (
                        len(left_segs) > 0
                        and len(right_segs) > 0
                        and all(
                            isinstance(s, dict) and ("start" in s) for s in left_segs
                        )
                        and all(
                            isinstance(s, dict) and ("start" in s) for s in right_segs
                        )
                    )
                    merged_segments: list[dict] = []
                    merged_text: str = ""
                    if can_merge_by_time:
                        li, ri = 0, 0
                        while li < len(left_segs) or ri < len(right_segs):
                            if ri >= len(right_segs) or (
                                li < len(left_segs)
                                and float(left_segs[li].get("start", 0.0))
                                <= float(right_segs[ri].get("start", 0.0))
                            ):
                                seg_src = left_segs[li]
                                li += 1
                            else:
                                seg_src = right_segs[ri]
                                ri += 1
                            seg_text = (seg_src.get("text") or "").strip()
                            if not seg_text:
                                continue
                            seg = {
                                "start": float(seg_src.get("start", 0.0)),
                                "end": float(
                                    seg_src.get("end", seg_src.get("start", 0.0))
                                ),
                                "text": seg_text,
                            }
                            merged_segments.append(seg)
                        if merged_segments:
                            merged_text = "\n".join(
                                [s["text"].strip() for s in merged_segments]
                            )
                    if not merged_text:
                        parts = [left_res.text.strip(), right_res.text.strip()]
                        parts = [p for p in parts if p]
                        merged_text = "\n\n".join(parts)
                        merged_segments = []
                    results.append(
                        TranscriptionResult(
                            text=merged_text,
                            language=left_res.language or right_res.language,
                            segments=merged_segments or None,
                        )
                    )
                return results
            finally:
                if cleanup_after:
                    try:
                        transcriber.full_cleanup()
                    except Exception:
                        pass

    def cleanup_all_transcribers(self) -> None:
        """
        Очистка всех зарегистрированных транскрибаторов
        """
        for name, transcriber in self.transcribers.items():
            try:
                transcriber.full_cleanup()
                logger.info(f"Очищен транскрибатор: {name}")
            except Exception as e:
                logger.warning(f"Ошибка очистки {name}: {e}")

    def compare_transcribers(
        self,
        audio_data: np.ndarray,
        sample_rate: int,
        transcriber_names: list[str] | None = None,
    ) -> dict[str, TranscriptionResult]:
        """
        Сравнивает результаты нескольких систем транскрибации

        Args:
            audio_data: Аудиоданные
            sample_rate: Частота дискретизации
            transcriber_names: Список имен систем для сравнения

        Returns:
            dict[str, TranscriptionResult]: Результаты транскрибации по системам
        """
        if transcriber_names is None:
            transcriber_names = self.list_transcribers()

        results = {}
        for name in transcriber_names:
            try:
                results[name] = self.transcribe(audio_data, sample_rate, name)
                logger.info(f"Транскрибация {name} выполнена успешно")
            except Exception as e:
                logger.error(f"Ошибка транскрибации {name}: {e}")

        return results
