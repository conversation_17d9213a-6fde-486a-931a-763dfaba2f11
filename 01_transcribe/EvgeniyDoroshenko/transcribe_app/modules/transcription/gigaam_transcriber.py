from __future__ import annotations

import logging
import os
import tempfile
from pathlib import Path

import gigaam
import numpy as np

from .base_transcriber import BaseTranscriber, TranscriptionResult

logger = logging.getLogger(__name__)


class GigaAMTranscriber(BaseTranscriber):
    """Транскрибатор GigaAM"""

    def __init__(
        self,
        model_name: str = "v2_rnnt",
        language: str = "ru",
    ) -> None:
        super().__init__(
            name="GigaAM",
            model_name=model_name,
            language=language,
            description="Транскрибатор GigaAM от Сбера",
            params={},
        )
        self.model = None

    def initialize(self) -> bool:
        try:
            self.model = gigaam.load_model(self.model_name)
            self.is_initialized = True
            logger.info("GigaAM model loaded (%s)", self.model_name)
            return True
        except Exception as exc:
            logger.error(f"Ошибка инициализации GigaAM: {exc}")
            return False

    def transcribe(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> TranscriptionResult:
        if not self.is_initialized and not self.initialize():
            return TranscriptionResult(text="")

        import soundfile as sf

        with tempfile.NamedTemporaryFile("wb", suffix=".wav", delete=False) as tmp:
            sf.write(tmp, audio_data, sample_rate)
            tmp_path = Path(tmp.name)

        try:
            transcription_result = self.model.transcribe_longform(str(tmp_path))

            # Проверяем, что результат — список сегментов
            if isinstance(transcription_result, list):
                segments = []
                texts = []
                for segment in transcription_result:
                    bounds = segment.get("boundaries") or [0.0, 0.0]
                    start = float(bounds[0]) if len(bounds) > 0 else 0.0
                    end = float(bounds[1]) if len(bounds) > 1 else start
                    transcription = (segment.get("transcription") or "").strip()
                    if transcription:
                        texts.append(transcription)
                        segments.append(
                            {"start": start, "end": end, "text": transcription}
                        )
                text = " ".join(texts).strip()
            elif isinstance(transcription_result, str):
                text = transcription_result
                segments = None
            else:
                text = ""
                segments = None

            return TranscriptionResult(
                text=text, language=self.language, segments=segments
            )
        except Exception as exc:
            logger.error("GigaAM transcription failed: %s", exc)
            return TranscriptionResult(text="")
        finally:
            try:
                os.remove(tmp_path)
            except Exception:
                pass
