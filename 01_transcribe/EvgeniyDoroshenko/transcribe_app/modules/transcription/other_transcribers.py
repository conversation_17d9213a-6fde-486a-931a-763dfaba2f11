import json
import logging
import os
from typing import List

import numpy as np

from .base_transcriber import BaseTranscriber, TranscriptionResult

logger = logging.getLogger(__name__)


class Wav2Vec2Transcriber(BaseTranscriber):
    """Транскрибатор Wav2Vec2"""

    def __init__(
        self,
        model_name: str = "jona<PERSON>grosman/wav2vec2-large-xlsr-53-russian",
        language: str = "ru",
    ):
        super().__init__(
            name="Wav2Vec2",
            model_name=model_name,
            language=language,
            description="Самообучающаяся модель от Facebook",
            params={},
        )
        self.processor = None

    def initialize(self) -> bool:
        """Инициализация Wav2Vec2"""
        try:
            import torch
            from transformers.models.wav2vec2 import Wav2Vec2ForCTC, Wav2Vec2Processor

            self.processor = Wav2Vec2Processor.from_pretrained(self.model_name)
            self.model = Wav2Vec2ForCTC.from_pretrained(self.model_name)
            self.torch = torch

            # Перевод модели в режим eval
            self.model.eval()

            self.is_initialized = True
            logger.info(f"Wav2Vec2 инициализирован: {self.model_name}")
            return True

        except ImportError as e:
            logger.error(f"Не удалось импортировать transformers: {e}")
            return False
        except Exception as e:
            logger.error(f"Ошибка инициализации Wav2Vec2: {e}")
            return False

    def transcribe(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> TranscriptionResult:
        """
        Транскрибация с помощью Wav2Vec2

        Args:
            audio_data: Аудиоданные
            sample_rate: Частота дискретизации

        Returns:
            TranscriptionResult: Результат транскрибации
        """
        try:
            # Wav2Vec2 обычно работает с 16kHz
            if sample_rate != 16000:
                import librosa

                audio_data = librosa.resample(
                    audio_data, orig_sr=sample_rate, target_sr=16000
                )

            # Обработка аудио
            inputs = self.processor(
                audio_data, sampling_rate=16000, return_tensors="pt", padding=True
            )

            # Инференс
            with self.torch.no_grad():
                logits = self.model(inputs.input_values).logits

            # Декодирование
            predicted_ids = self.torch.argmax(logits, dim=-1)
            transcription = self.processor.batch_decode(predicted_ids)[0]

            result = TranscriptionResult(text=transcription, language=self.language)

            logger.info(f"Wav2Vec2: транскрибировано {len(transcription)} символов")
            return result

        except Exception as e:
            logger.error(f"Ошибка в Wav2Vec2: {e}")
            return TranscriptionResult(text="")


class VoskTranscriber(BaseTranscriber):
    """Транскрибатор Vosk"""

    def __init__(self, model_path: str = "", language: str = "ru"):
        super().__init__(
            name="Vosk",
            model_name="vosk-model-ru-0.42",
            language=language,
            description="Легковесная оффлайн система распознавания",
            params={"model_path": model_path},
        )
        self.rec = None

    def initialize(self) -> bool:
        """Инициализация Vosk"""
        try:
            import vosk

            model_path = self.params.get("model_path", "")
            if not model_path or not os.path.exists(model_path):
                logger.warning(f"Модель Vosk не найдена по пути: {model_path}")
                return False

            # Подавляем логи Vosk
            vosk.SetLogLevel(-1)

            self.vosk_model = vosk.Model(model_path)
            self.rec = vosk.KaldiRecognizer(self.vosk_model, 16000)
            self.vosk = vosk

            self.is_initialized = True
            logger.info(f"Vosk инициализирован с моделью: {model_path}")
            return True

        except ImportError as e:
            logger.error(f"Не удалось импортировать vosk: {e}")
            return False
        except Exception as e:
            logger.error(f"Ошибка инициализации Vosk: {e}")
            return False

    def transcribe(
        self, audio_data: np.ndarray, sample_rate: int
    ) -> TranscriptionResult:
        """
        Транскрибация с помощью Vosk

        Args:
            audio_data: Аудиоданные
            sample_rate: Частота дискретизации

        Returns:
            TranscriptionResult: Результат транскрибации
        """
        try:
            # Vosk работает с 16kHz
            if sample_rate != 16000:
                import librosa

                audio_data = librosa.resample(
                    audio_data, orig_sr=sample_rate, target_sr=16000
                )

            # Конвертируем в 16-bit PCM
            audio_int16 = (audio_data * 32767).astype(np.int16)
            audio_bytes = audio_int16.tobytes()

            # Обработка аудио порциями
            self.rec.Reset()
            chunk_size = 4000  # Размер порции
            results = []
            segments = []

            def _append_segment_from_json(obj: dict) -> None:
                text = (obj.get("text") or "").strip()
                words = obj.get("result") or []
                if text:
                    results.append(text)
                    if isinstance(words, list) and len(words) > 0:
                        start = float(words[0].get("start", 0.0))
                        end = float(words[-1].get("end", start))
                        segments.append({"start": start, "end": end, "text": text})

            for i in range(0, len(audio_bytes), chunk_size):
                chunk = audio_bytes[i : i + chunk_size]
                if self.rec.AcceptWaveform(chunk):
                    try:
                        obj = json.loads(self.rec.Result())
                        _append_segment_from_json(obj)
                    except Exception:
                        pass

            # Финальный результат
            try:
                final_obj = json.loads(self.rec.FinalResult())
                _append_segment_from_json(final_obj)
            except Exception:
                pass

            # Объединяем результаты
            full_text = " ".join(results).strip()

            result = TranscriptionResult(
                text=full_text, language=self.language, segments=segments or None
            )

            logger.info(f"Vosk: транскрибировано {len(full_text)} символов")
            return result

        except Exception as e:
            logger.error(f"Ошибка в Vosk: {e}")
            return TranscriptionResult(text="")
