import concurrent.futures
import logging
import os
import tempfile
import time
from datetime import datetime

import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="[%(asctime)s] %(name)s > %(levelname)s: %(message)s"
)
logger = logging.getLogger(__name__)

# Импорты модулей приложения
from config.settings import ENHANCEMENT_METHODS, STREAMLIT_CONFIG
from modules.audio_enhancement.base_enhancer import EnhancementPipeline

# Импорты модулей улучшения аудио
from modules.audio_enhancement.noise_reduction import (
    DeepFilterNetEnhancer,
    FFmpegEnhancer,
    NoiseReduceEnhancer,
    RNNoiseEnhancer,
    SpectralSubtractionEnhancer,
    WienerFilterEnhancer,
)

# Импорты модулей сравнения
from modules.comparison.text_comparison import ComparisonVisualizer, TextComparator

# Импорты модулей транскрибации
from modules.transcription.base_transcriber import TranscriptionManager
from modules.transcription.gigaam_transcriber import GigaAMTranscriber
from modules.transcription.nemo_transcriber import NeMoTranscriber
from modules.transcription.other_transcribers import (
    VoskTranscriber,
    Wav2Vec2Transcriber,
)
from modules.transcription.tone_transcriber import ToneTranscriber
from modules.transcription.whisper_transcribers import (
    FasterWhisperTranscriber,
    OpenAIWhisperTranscriber,
    WhisperXTranscriber,
)
from modules.utils.audio_utils import AudioProcessor

# Конфигурация страницы
st.set_page_config(**STREAMLIT_CONFIG)  # type: ignore


class AudioAnalysisApp:
    """Главный класс приложения"""

    def __init__(self):
        self.audio_processor = AudioProcessor()
        self.enhancement_pipeline = EnhancementPipeline()
        self.transcription_manager = TranscriptionManager()
        self.text_comparator = TextComparator()
        self.comparison_visualizer = ComparisonVisualizer()

        # Инициализируем состояние сессии
        self._initialize_session_state()

        # Регистрируем доступные системы транскрибации
        self._register_transcribers()

    def _initialize_session_state(self):
        """Инициализация состояния сессии для поддержки нескольких файлов"""
        if "files" not in st.session_state:
            st.session_state.files = []
        if "selected_file_index" not in st.session_state:
            st.session_state.selected_file_index = 0
        if "model_comparison_results" not in st.session_state:
            st.session_state.model_comparison_results = None
        if "batch_comparison_results" not in st.session_state:
            st.session_state.batch_comparison_results = None

    def _register_transcribers(self):
        """Регистрация доступных систем транскрибации"""

        # Whisper
        try:
            self.transcription_manager.register_transcriber(WhisperXTranscriber())
        except Exception as e:
            logger.warning(f"WhisperX недоступен: {e}")

        try:
            self.transcription_manager.register_transcriber(FasterWhisperTranscriber())
        except Exception as e:
            logger.warning(f"Faster Whisper недоступен: {e}")

        try:
            self.transcription_manager.register_transcriber(OpenAIWhisperTranscriber())
        except Exception as e:
            logger.warning(f"OpenAI Whisper недоступен: {e}")

        # Wav2Vec2
        try:
            self.transcription_manager.register_transcriber(Wav2Vec2Transcriber())
        except Exception as e:
            logger.warning("Wav2Vec2 не доступен: {e}")

        # Vosk
        try:
            self.transcription_manager.register_transcriber(
                VoskTranscriber(model_path="vosk-model-ru-0.42")
            )
        except Exception as e:
            logger.warning("Vosk не доступен: {e}")

        # NeMo ASR
        try:
            self.transcription_manager.register_transcriber(NeMoTranscriber())
        except Exception as exc:
            logger.warning("NeMo not available: %s", exc)

        # GigaAM
        try:
            self.transcription_manager.register_transcriber(GigaAMTranscriber())
        except Exception as exc:
            logger.warning("GigaAM not available: %s", exc)

        # T-one
        try:
            self.transcription_manager.register_transcriber(ToneTranscriber())
        except Exception as e:
            logger.warning(f"T-one недоступен: {e}")

    def run(self):
        """Основной метод запуска приложения"""

        st.title("🎙️ Анализатор аудиозаписей и транскрибации")
        st.text(
            "Улучшение качества аудио, транскрибация и сравнение с эталонным текстом"
        )

        tab1, tab2, tab3, tab4 = st.tabs(
            [
                "📁 Загрузка аудио",
                "🔧 Улучшение качества",
                "📝 Транскрибация",
                "🆚 Сравнение моделей",
            ]
        )

        with tab1:
            self._audio_upload_page()

        with tab2:
            self._audio_enhancement_page()

        with tab3:
            self._transcription_page()

        with tab4:
            self._model_comparison_page()

    # ========================================
    # Вкладка "📁 Загрузка аудио"
    # ========================================
    def _audio_upload_page(self):
        """Страница загрузки аудио"""

        st.header("📁 Загрузка аудиофайлов")

        uploaded_files = st.file_uploader(
            "Выберите аудиофайлы",
            type=["wav", "mp3"],
            help="Поддерживаемые форматы: WAV, MP3",
            accept_multiple_files=True,
        )

        if uploaded_files:
            for uploaded_file in uploaded_files:
                # Проверяем, был ли файл уже загружен
                if any(f["name"] == uploaded_file.name for f in st.session_state.files):
                    continue

                try:
                    with tempfile.NamedTemporaryFile(
                        delete=False, suffix=f".{uploaded_file.name.split('.')[-1]}"
                    ) as tmp_file:
                        tmp_file.write(uploaded_file.getvalue())
                        temp_path = tmp_file.name

                    with st.spinner(f"Загрузка {uploaded_file.name}..."):
                        (left_channel, right_channel), sample_rate = (
                            self.audio_processor.load_audio(temp_path)
                        )
                        audio_data = (left_channel + right_channel) / 2

                        # Создаем словарь для хранения информации о файле
                        file_data = {
                            "name": uploaded_file.name,
                            "audio_data": audio_data,
                            "sample_rate": sample_rate,
                            "left_channel_data": left_channel,
                            "right_channel_data": right_channel,
                            "enhanced_left_channel": None,
                            "enhanced_right_channel": None,
                            "enhanced_audio": None,
                            "enhanced_sample_rate": None,
                            "transcription_result": None,
                            "comparison_result": None,
                            "raw_bytes": uploaded_file.getvalue(),
                        }
                        st.session_state.files.append(file_data)

                    os.unlink(temp_path)

                except Exception as e:
                    st.error(f"Ошибка загрузки файла {uploaded_file.name}: {str(e)}")
                    logger.error(f"Ошибка загрузки {uploaded_file.name}: {e}")

        if st.session_state.files:
            file_names = [f["name"] for f in st.session_state.files]
            st.session_state.selected_file_index = st.selectbox(
                "Выберите файл для анализа:",
                range(len(file_names)),
                format_func=lambda i: file_names[i],
            )

            selected_file = st.session_state.files[st.session_state.selected_file_index]

            # Отображаем информацию об аудио с каналами
            self._display_stereo_audio_info(
                selected_file["left_channel_data"],
                selected_file["right_channel_data"],
                selected_file["sample_rate"],
                selected_file["name"],
            )

            # Воспроизводим оба канала
            st.audio(
                selected_file["raw_bytes"],
                format=f"audio/{selected_file['name'].split('.')[-1]}",
            )

    def _display_stereo_audio_info(
        self,
        left_channel: np.ndarray,
        right_channel: np.ndarray,
        sample_rate: int,
        filename: str,
    ):
        """Отображение информации об аудиофайле с каналами"""
        duration = len(left_channel) / sample_rate

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Длительность", f"{duration:.2f} сек")
        with col2:
            st.metric("Частота дискретизации", f"{sample_rate} Гц")
        with col3:
            st.metric("Режим", "Стерео (2 канала)")

        # Визуализация каналов
        fig = go.Figure()
        time_axis = np.linspace(0, duration, len(left_channel))

        fig.add_trace(
            go.Scatter(
                x=time_axis,
                y=left_channel,
                mode="lines",
                name="Левый канал (Менеджер)",
                line=dict(color="aqua", width=1),
            )
        )

        fig.add_trace(
            go.Scatter(
                x=time_axis,
                y=right_channel,
                mode="lines",
                name="Правый канал (Клиент)",
                line=dict(color="yellow", width=1),
            )
        )

        fig.update_layout(
            title="Форма звуковых волн по каналам",
            xaxis_title="Время (сек)",
            yaxis_title="Амплитуда",
            height=300,
        )

        st.plotly_chart(fig, use_container_width=True)

    def _display_audio_info(
        self, audio_data: np.ndarray, sample_rate: int, filename: str
    ):
        """Отображение информации об аудиофайле"""

        duration = len(audio_data) / sample_rate

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Файл", filename)
        with col2:
            st.metric("Длительность", f"{duration:.2f} сек")
        with col3:
            st.metric("Частота дискретизации", f"{sample_rate} Гц")
        with col4:
            st.metric("Количество семплов", len(audio_data))

        # Визуализация формы волны
        fig = go.Figure()
        time_axis = np.linspace(0, duration, len(audio_data))

        fig.add_trace(
            go.Scatter(
                x=time_axis,
                y=audio_data,
                mode="lines",
                name="Амплитуда",
                line=dict(color="blue", width=1),
            )
        )

        fig.update_layout(
            title="Форма звуковой волны",
            xaxis_title="Время (сек)",
            yaxis_title="Амплитуда",
            height=300,
        )

        st.plotly_chart(fig, use_container_width=True)

    # ========================================
    # Вкладка "🔧 Улучшение качества аудио"
    # ========================================
    def _audio_enhancement_page(self):
        """Страница улучшения аудио"""

        st.header("🔧 Улучшение качества аудио")

        if not st.session_state.files:
            st.warning("Сначала загрузите аудиофайл на вкладке 'Загрузка аудио'")
            return

        selected_file = st.session_state.files[st.session_state.selected_file_index]

        # Выбор методов улучшения
        st.subheader("Выберите методы улучшения")

        enhancement_methods = st.multiselect(
            "Методы обработки",
            options=list(ENHANCEMENT_METHODS.keys()),
            default=[],
            format_func=lambda x: ENHANCEMENT_METHODS[x]["name"],
        )

        # Настройки для каждого метода
        method_params = {}

        for method in enhancement_methods:
            with st.expander(f"Настройки: {ENHANCEMENT_METHODS[method]['name']}"):
                st.write(ENHANCEMENT_METHODS[method]["description"])

                if method == "ffmpeg":
                    # Слайдеры по умолчанию из ENHANCEMENT_METHODS[method]["params"]
                    highpass_freq = st.slider(
                        "Высокочастотный фильтр (Hz)",
                        0,
                        500,
                        ENHANCEMENT_METHODS[method]["params"]["highpass_freq"],
                        step=10,
                        key=f"{method}_highpass_freq",
                    )
                    lowpass_freq = st.slider(
                        "Низкочастотный фильтр (Hz)",
                        1000,
                        8000,
                        ENHANCEMENT_METHODS[method]["params"]["lowpass_freq"],
                        step=50,
                        key=f"{method}_lowpass_freq",
                    )

                    loudnorm_i = st.slider(
                        "Интегрированная громкость I (LUFS)",
                        -30.0,
                        -16.0,
                        float(ENHANCEMENT_METHODS[method]["params"]["loudnorm_i"]),
                        step=0.1,
                        key=f"{method}_loudnorm_i",
                    )
                    loudnorm_tp = st.slider(
                        "Пиковый уровень TP (dBFS)",
                        -6.0,
                        -1.0,
                        float(ENHANCEMENT_METHODS[method]["params"]["loudnorm_tp"]),
                        step=0.1,
                        key=f"{method}_loudnorm_tp",
                    )
                    loudnorm_lra = st.slider(
                        "Диапазон громкости LRA (LU)",
                        1.0,
                        20.0,
                        float(ENHANCEMENT_METHODS[method]["params"]["loudnorm_lra"]),
                        step=0.5,
                        key=f"{method}_loudnorm_lra",
                    )
                    target_sample_rate = st.selectbox(
                        "Частота дискретизации (Hz)",
                        [8000, 16000, 22050, 44100, 48000],
                        index=[8000, 16000, 22050, 44100, 48000].index(
                            ENHANCEMENT_METHODS[method]["params"]["target_sample_rate"]
                        ),
                        key=f"{method}_target_sample_rate",
                    )
                    sample_fmt = st.selectbox(
                        "Формат сэмплов",
                        ["s16", "s24", "s32", "f32", "f64"],
                        index=["s16", "s24", "s32", "f32", "f64"].index(
                            ENHANCEMENT_METHODS[method]["params"]["sample_fmt"]
                        ),
                        key=f"{method}_sample_fmt",
                    )

                    method_params[method] = {
                        "highpass_freq": highpass_freq,
                        "lowpass_freq": lowpass_freq,
                        "loudnorm_i": loudnorm_i,
                        "loudnorm_tp": loudnorm_tp,
                        "loudnorm_lra": loudnorm_lra,
                        "target_sample_rate": target_sample_rate,
                        "sample_fmt": sample_fmt,
                    }

                elif method == "noisereduce":
                    stationary = st.checkbox(
                        "Стационарный шум", value=True, key=f"{method}_stationary"
                    )
                    prop_decrease = st.slider(
                        "Степень подавления",
                        0.1,
                        1.0,
                        0.8,
                        key=f"{method}_prop_decrease",
                    )
                    method_params[method] = {
                        "stationary": stationary,
                        "prop_decrease": prop_decrease,
                    }

                elif method == "spectral_subtraction":
                    alpha = st.slider("Alpha", 0.5, 5.0, 2.0, key=f"{method}_alpha")
                    beta = st.slider("Beta", 0.01, 0.5, 0.01, key=f"{method}_beta")
                    method_params[method] = {"alpha": alpha, "beta": beta}

        # Кнопка обработки
        if st.button("🚀 Применить улучшения", type="primary"):
            if not enhancement_methods:
                st.warning("Выберите хотя бы один метод улучшения")
                return

            with st.spinner("Обработка аудио..."):
                result = self._process_audio_enhancement(
                    selected_file["left_channel_data"],
                    selected_file["right_channel_data"],
                    selected_file["sample_rate"],
                    enhancement_methods,
                    method_params,
                )

                if result:
                    enhanced_left, enhanced_right, enhanced_sr = result

                    min_len = min(len(enhanced_left), len(enhanced_right))
                    enhanced_left = enhanced_left[:min_len]
                    enhanced_right = enhanced_right[:min_len]

                    selected_file["enhanced_left_channel"] = enhanced_left
                    selected_file["enhanced_right_channel"] = enhanced_right
                    selected_file["enhanced_sample_rate"] = enhanced_sr
                    selected_file["enhanced_audio"] = (
                        enhanced_left + enhanced_right
                    ) / 2
                    st.success("Аудио успешно обработано!")

        # Сравнение до и после
        if selected_file["enhanced_audio"] is not None:
            self._display_audio_comparison()

    def _process_audio_enhancement(
        self,
        left_channel: np.ndarray,
        right_channel: np.ndarray,
        sample_rate: int,
        methods: list,
        params: dict,
    ) -> tuple[np.ndarray, np.ndarray, int] | None:
        """Обработка аудио выбранными методами для каждого канала"""

        try:
            # Очищаем пайплайн
            self.enhancement_pipeline = EnhancementPipeline()

            # Добавляем выбранные методы
            for method in methods:
                if method == "ffmpeg":
                    enhancer = FFmpegEnhancer(**params.get(method, {}))
                elif method == "noisereduce":
                    enhancer = NoiseReduceEnhancer(**params.get(method, {}))
                elif method == "spectral_subtraction":
                    enhancer = SpectralSubtractionEnhancer(**params.get(method, {}))
                elif method == "wiener_filter":
                    enhancer = WienerFilterEnhancer()
                elif method == "deepfilternet":
                    enhancer = DeepFilterNetEnhancer()
                elif method == "rnnoise":
                    enhancer = RNNoiseEnhancer()
                else:
                    continue

                self.enhancement_pipeline.add_enhancer(enhancer)

            # Применяем пайплайн
            enhanced_left, enhanced_right, enhanced_sr = (
                self.enhancement_pipeline.process(
                    left_channel, right_channel, sample_rate
                )
            )
            return enhanced_left, enhanced_right, enhanced_sr

        except Exception as e:
            st.error(f"Ошибка обработки аудио: {str(e)}")
            logger.error(f"Ошибка в обработке аудио: {e}", exc_info=True)
            return None

    def _display_audio_comparison(self):
        """Отображение сравнения исходного и обработанного аудио."""
        selected_file = st.session_state.files[st.session_state.selected_file_index]

        st.subheader("Сравнение результатов")

        col1, col2 = st.columns(2)

        # --- ИСХОДНОЕ АУДИО ---
        with col1:
            st.markdown("**Исходное аудио**")
            # Передаем numpy-массив и его частоту напрямую
            st.audio(
                selected_file["audio_data"], sample_rate=selected_file["sample_rate"]
            )

        # --- ОБРАБОТАННОЕ АУДИО ---
        with col2:
            st.markdown("**Обработанное аудио**")
            # Получаем корректную частоту для обработанного аудио
            sr_to_use = (
                selected_file.get("enhanced_sample_rate")
                or selected_file["sample_rate"]
            )

            # Передаем numpy-массив и его НОВУЮ частоту напрямую
            st.audio(selected_file["enhanced_audio"], sample_rate=sr_to_use)

        # Визуализация спектрограмм
        self._display_spectrograms_comparison()

    def _audio_to_bytes(self, audio_data: np.ndarray, sample_rate: int) -> bytes:
        """Конвертация аудио в байты для воспроизведения"""
        import io

        import soundfile as sf

        buffer = io.BytesIO()
        sf.write(buffer, audio_data, sample_rate, format="WAV")
        return buffer.getvalue()

    def _display_spectrograms_comparison(self):
        """Отображение сравнения спектрограмм"""
        selected_file = st.session_state.files[st.session_state.selected_file_index]
        try:
            import librosa

            # Вычисляем спектрограммы
            original_stft = librosa.stft(selected_file["audio_data"])
            enhanced_stft = librosa.stft(selected_file["enhanced_audio"])

            # Конвертируем в дБ
            original_db = librosa.amplitude_to_db(np.abs(original_stft))
            enhanced_db = librosa.amplitude_to_db(np.abs(enhanced_stft))

            # Создаем фигуры
            fig = go.Figure()

            # Добавляем спектрограммы
            fig.add_trace(
                go.Heatmap(
                    z=original_db, colorscale="Viridis", name="Исходное", visible=True
                )
            )

            fig.add_trace(
                go.Heatmap(
                    z=enhanced_db,
                    colorscale="Viridis",
                    name="Обработанное",
                    visible=False,
                )
            )

            # Добавляем кнопки переключения
            fig.update_layout(
                title="Спектрограммы аудио",
                updatemenus=[
                    dict(
                        type="buttons",
                        direction="left",
                        buttons=list(
                            [
                                dict(
                                    args=[{"visible": [True, False]}],
                                    label="Исходное",
                                    method="restyle",
                                ),
                                dict(
                                    args=[{"visible": [False, True]}],
                                    label="Обработанное",
                                    method="restyle",
                                ),
                            ]
                        ),
                        pad={"r": 10, "t": 10},
                        showactive=True,
                        x=0.01,
                        xanchor="left",
                        y=1.02,
                        yanchor="top",
                    ),
                ],
            )

            st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.warning(f"Не удалось построить спектрограммы: {e}")

    # ========================================
    # Вкладка "📝 Транскрибация аудио"
    # ========================================
    def _transcription_page(self):
        """Страница транскрибации"""
        st.header("📝 Транскрибация аудио")

        if not st.session_state.files:
            st.warning("Сначала загрузите аудиофайл")
            return

        selected_file = st.session_state.files[st.session_state.selected_file_index]

        # Выбор аудио для транскрибации
        audio_choice = st.radio(
            "Какое аудио использовать для транскрибации?",
            (
                ["Исходное", "Обработанное"]
                if selected_file["enhanced_audio"] is not None
                else ["Исходное"]
            ),
            key=f"transcription_audio_choice_{selected_file['name']}",
        )

        # Выбор системы транскрибации
        available_transcribers = self.transcription_manager.list_transcribers()
        selected_transcriber = st.selectbox(
            "Система транскрибации",
            available_transcribers,
            help="Выберите систему для транскрибации аудио",
        )

        # Настройки транскрибации
        with st.expander("Дополнительные настройки"):
            language = st.selectbox("Язык", ["ru"], index=0)

        col1, col2 = st.columns(2)
        with col1:
            if st.button(
                "🎯 Запустить транскрибацию для текущего файла", type="primary"
            ):
                self._transcribe_single_file(
                    selected_file, audio_choice, selected_transcriber, language
                )

        with col2:
            if st.button("🚀 Запустить транскрибацию для всех файлов"):
                self._transcribe_all_files(audio_choice, selected_transcriber, language)

        # Отображение результатов транскрибации
        if selected_file["transcription_result"]:
            self._display_transcription_results()

    def _transcribe_single_file(
        self, file_data, audio_choice, transcriber_name, language
    ):
        # Предпочитаем раздельную транскрибацию каналов с объединением в диалог
        if (
            audio_choice == "Обработанное"
            and file_data.get("enhanced_left_channel") is not None
            and file_data.get("enhanced_right_channel") is not None
        ):
            left = file_data["enhanced_left_channel"]
            right = file_data["enhanced_right_channel"]
            sr = file_data.get("enhanced_sample_rate") or file_data["sample_rate"]
        else:
            left = file_data.get("left_channel_data")
            right = file_data.get("right_channel_data")
            sr = file_data["sample_rate"]

        with st.spinner(f"Транскрибация {file_data['name']}..."):
            result = None
            if left is not None and right is not None:
                result = self._perform_transcription_stereo(
                    left,
                    right,
                    sr,
                    transcriber_name,
                    language,
                )
            # Фоллбек на моно, если вдруг нет раздельных каналов
            if result is None and file_data.get("audio_data") is not None:
                result = self._perform_transcription(
                    file_data["audio_data"], sr, transcriber_name, language
                )

            if result:
                file_data["transcription_result"] = result
                st.success(f"Транскрибация {file_data['name']} выполнена успешно!")

    def _transcribe_all_files(self, audio_choice, transcriber_name, language):
        progress_bar = st.progress(0)
        status_text = st.empty()
        total_files = len(st.session_state.files)

        # Попытка батч-стерео, если у всех файлов есть нужные каналы
        stereo_items: list[tuple[np.ndarray, np.ndarray, int]] = []
        indices: list[int] = []
        all_stereo_ok = True
        for idx, f in enumerate(st.session_state.files):
            if (
                audio_choice == "Обработанное"
                and f.get("enhanced_left_channel") is not None
                and f.get("enhanced_right_channel") is not None
            ):
                left = f["enhanced_left_channel"]
                right = f["enhanced_right_channel"]
                sr = f.get("enhanced_sample_rate") or f["sample_rate"]
            else:
                left = f.get("left_channel_data")
                right = f.get("right_channel_data")
                sr = f.get("sample_rate")
            if left is None or right is None or sr is None:
                all_stereo_ok = False
                break
            stereo_items.append((left, right, int(sr)))
            indices.append(idx)

        # Если можем — используем батч-режим стерео
        if all_stereo_ok and len(stereo_items) == total_files:
            try:
                transcriber = self.transcription_manager.get_transcriber(
                    transcriber_name
                )
                if transcriber:
                    transcriber.language = language
                status_text.text("Батчевая стерео-транскрибация...")
                results = self.transcription_manager.transcribe_stereo_dialog_many(
                    stereo_items, transcriber_name
                )
                for i, idx in enumerate(indices):
                    st.session_state.files[idx]["transcription_result"] = results[i]
                    progress_bar.progress((i + 1) / total_files)
                status_text.success("Пакетная транскрибация завершена!")
                return
            except Exception as exc:
                logger.warning(
                    "Не удалось выполнить батчевую стерео-транскрибацию, переключаюсь на пофайловую: %s",
                    exc,
                )
                # Фоллбэк на пофайловую обработку ниже

        # Пофайловая обработка (фоллбэк)
        for i, file_data in enumerate(st.session_state.files):
            status_text.text(
                f"Транскрибация файла {i + 1}/{total_files}: {file_data['name']}"
            )
            self._transcribe_single_file(
                file_data, audio_choice, transcriber_name, language
            )
            progress_bar.progress((i + 1) / total_files)
        status_text.success("Пакетная транскрибация завершена!")

    def _perform_transcription(
        self,
        audio_data: np.ndarray,
        sample_rate: int,
        transcriber_name: str,
        language: str,
    ):
        """Выполнение транскрибации"""

        try:
            # Обновляем язык в транскрибере
            transcriber = self.transcription_manager.get_transcriber(transcriber_name)
            if transcriber:
                transcriber.language = language

            # Выполняем транскрибацию
            start_time = time.time()
            result = self.transcription_manager.transcribe(
                audio_data, sample_rate, transcriber_name
            )
            processing_time = time.time() - start_time
            if result:
                result.processing_time = processing_time
            return result
        except Exception as e:
            st.error(f"Ошибка транскрибации: {str(e)}")
            logger.error(f"Ошибка транскрибации: {e}")
            return None

    def _perform_transcription_stereo(
        self,
        left_channel: np.ndarray,
        right_channel: np.ndarray,
        sample_rate: int,
        transcriber_name: str,
        language: str,
    ):
        """Транскрибация стерео: по каналам отдельно с объединением в диалог."""
        try:
            # Обновляем язык в транскрибере
            transcriber = self.transcription_manager.get_transcriber(transcriber_name)
            if transcriber:
                transcriber.language = language

            # Выполняем транскрибацию двух каналов и объединяем
            start_time = time.time()
            result = self.transcription_manager.transcribe_stereo_dialog(
                left_channel,
                right_channel,
                sample_rate,
                transcriber_name,
            )
            processing_time = time.time() - start_time
            if result:
                result.processing_time = processing_time
            return result
        except Exception as e:
            st.error(f"Ошибка стерео-транскрибации: {str(e)}")
            logger.error(f"Ошибка стерео-транскрибации: {e}")
            return None

    def _display_transcription_results(self):
        """Отображение результатов транскрибации"""
        selected_file = st.session_state.files[st.session_state.selected_file_index]
        result = selected_file["transcription_result"]

        st.subheader("Результат транскрибации")

        # Основная информация
        col1, col2 = st.columns(2)

        with col1:
            if result.processing_time is not None:
                st.metric("Время обработки", f"{result.processing_time:.2f} сек")
            else:
                st.metric("Время обработки", "—")
        with col2:
            st.metric("Язык", result.language or "—")

        # Текст транскрибации
        display_text = result.text or ""
        if (not display_text) and result.segments and isinstance(result.segments, list):
            try:
                parts = [
                    (s.get("text", "") or "").strip()
                    for s in result.segments
                    if isinstance(s, dict) and (s.get("text") or "").strip()
                ]
                display_text = "\n".join(parts)
            except Exception:
                pass
        st.text_area(
            "Транскрибированный текст",
            value=display_text,
            height=200,
            help="Скопируйте текст для дальнейшего использования",
        )

        # Сегменты (если доступны)
        if result.segments:
            # Фильтруем пустые строки, чтобы не показывать пустые строки
            segs = [
                s
                for s in result.segments
                if isinstance(s, dict) and (s.get("text") or "").strip()
            ]
            if segs:
                with st.expander("Детальная информация по сегментам"):
                    segments_df = pd.DataFrame(segs)
                    st.dataframe(segments_df, use_container_width=True)

    # ========================================
    # Вкладка "🆚 Сравнение моделей транскрибации"
    # ========================================
    def _model_comparison_page(self):
        """Страница сравнения нескольких моделей транскрибации"""

        st.header("🆚 Сравнение моделей транскрибации")

        if not st.session_state.files:
            st.warning("Сначала загрузите аудиофайл на вкладке 'Загрузка аудио'")
            return

        selected_file = st.session_state.files[st.session_state.selected_file_index]

        # Выбор аудио для транскрибации
        audio_choice = st.radio(
            "Какое аудио использовать для транскрибации?",
            (
                ["Исходное", "Обработанное"]
                if selected_file["enhanced_audio"] is not None
                else ["Исходное"]
            ),
            key="model_comparison_audio_choice",
        )

        # Выбор моделей для сравнения
        available_transcribers = self.transcription_manager.list_transcribers()

        st.subheader("Выберите модели для сравнения")
        selected_models = st.multiselect(
            "Модели транскрибации",
            available_transcribers,
            default=available_transcribers,
            help="Выберите несколько моделей для сравнения их производительности",
        )

        if not selected_models:
            st.warning("Выберите хотя бы одну модель для сравнения")
            return

        # Настройки сравнения
        with st.expander("Настройки сравнения"):
            col1, col2 = st.columns(2)

            with col1:
                language = st.selectbox(
                    "Язык", ["ru"], index=0, key="comparison_language"
                )
                parallel_processing = st.checkbox(
                    "Параллельная обработка",
                    value=False,
                    help="Обрабатывать модели параллельно для экономии времени",
                )

            with col2:
                max_workers = st.slider(
                    "Максимум потоков",
                    1,
                    len(selected_models),
                    1,
                    help="Количество одновременно работающих моделей",
                )
                timeout = st.number_input(
                    "Таймаут (сек)",
                    30,
                    600,
                    300,
                    help="Максимальное время ожидания для каждой модели",
                )

        # Кнопка запуска сравнения
        if st.button("🚀 Запустить сравнение для всех файлов", type="primary"):
            self._run_batch_model_comparison(
                audio_choice,
                selected_models,
                language,
                parallel_processing,
                max_workers,
                timeout,
            )

        # Отображение результатов сравнения
        if st.session_state.batch_comparison_results:
            self._display_batch_model_comparison_results(selected_models)

    def _run_batch_model_comparison(
        self, audio_choice, selected_models, language, parallel, max_workers, timeout
    ):
        st.session_state.batch_comparison_results = []
        progress_bar = st.progress(0)
        status_text = st.empty()
        total_files = len(st.session_state.files)

        for i, file_data in enumerate(st.session_state.files):
            status_text.text(
                f"Обработка файла {i + 1}/{total_files}: {file_data['name']}"
            )
            # Выбираем каналы для стерео-транскрибации
            if (
                audio_choice == "Обработанное"
                and file_data.get("enhanced_left_channel") is not None
                and file_data.get("enhanced_right_channel") is not None
            ):
                left = file_data["enhanced_left_channel"]
                right = file_data["enhanced_right_channel"]
                sr_to_use = (
                    file_data.get("enhanced_sample_rate") or file_data["sample_rate"]
                )
            else:
                left = file_data.get("left_channel_data")
                right = file_data.get("right_channel_data")
                sr_to_use = file_data["sample_rate"]

            results = self._run_model_comparison_stereo(
                left,
                right,
                sr_to_use,
                selected_models,
                language,
                parallel,
                max_workers,
                timeout,
            )
            st.session_state.batch_comparison_results.append(
                {"file_name": file_data["name"], "results": results}
            )
            progress_bar.progress((i + 1) / total_files)

        status_text.success("Сравнение всех файлов завершено!")

    def _display_batch_model_comparison_results(self, selected_models):
        st.subheader("📊 Сводный отчет по всем файлам")
        report_data = []
        for file_result in st.session_state.batch_comparison_results:
            row = {"Имя файла": file_result["file_name"]}
            for result in file_result["results"]:
                row[result["model_name"]] = result["text"]
            report_data.append(row)

        df = pd.DataFrame(report_data)
        st.dataframe(df, use_container_width=True)

        csv = df.to_csv(index=False).encode("utf-8")
        st.download_button(
            label="📥 Скачать сводный отчет в CSV",
            data=csv,
            file_name="batch_comparison_report.csv",
            mime="text/csv",
        )

        st.markdown("---")
        st.subheader("🎯 Оценка моделей по эталонному тексту")
        if not st.session_state.batch_comparison_results:
            st.info("Нет данных для оценки")
            return

        file_names = [
            fr["file_name"] for fr in st.session_state.batch_comparison_results
        ]
        sel_file = st.selectbox("Файл для оценки", options=file_names)
        file_block = next(
            (
                fr
                for fr in st.session_state.batch_comparison_results
                if fr["file_name"] == sel_file
            ),
            None,
        )
        if not file_block:
            st.warning("Не найден блок результатов для выбранного файла")
            return

        # Источник эталона
        ref_mode = st.radio(
            "Источник эталонного текста",
            ["Ввести вручную", "Загрузить TXT", "Выбрать из моделей"],
            horizontal=True,
        )
        reference_text: str | None = None
        if ref_mode == "Ввести вручную":
            reference_text = st.text_area(
                "Эталонный текст", height=150, placeholder="Вставьте правильный текст…"
            )
        elif ref_mode == "Загрузить TXT":
            uploaded = st.file_uploader(
                "Загрузите файл .txt с эталоном",
                type=["txt"],
                accept_multiple_files=False,
            )
            if uploaded is not None:
                try:
                    reference_text = uploaded.read().decode("utf-8", errors="ignore")
                    st.success("Эталонный текст загружен")
                except Exception as e:
                    st.error(f"Ошибка чтения файла: {e}")
        else:
            # Выбор одной из моделей как эталона
            model_names = [res["model_name"] for res in file_block["results"]]
            ref_model = st.selectbox(
                "Выбрать модель в качестве эталона", options=model_names
            )
            picked = next(
                (r for r in file_block["results"] if r["model_name"] == ref_model), None
            )
            reference_text = picked["text"] if picked else None

        st.caption(
            "Нормализация: нижний регистр, удаление знаков препинания, схлопывание пробелов (применяется автоматически)"
        )
        ignore_order = st.checkbox(
            "Игнорировать порядок реплик (сортировать строки перед сравнением)",
            value=False,
        )

        if (reference_text or "") and st.button(
            "🔎 Рассчитать метрики", type="primary"
        ):

            def _sort_lines_for_comparison(text: str) -> str:
                lines = [ln.strip() for ln in str(text).splitlines() if ln.strip()]
                return (
                    "\n".join(sorted(lines, key=lambda s: s.lower()))
                    if len(lines) > 1
                    else str(text).strip()
                )

            # Считаем метрики для всех выбранных моделей
            rows = []
            for res in file_block["results"]:
                model = res["model_name"]
                hyp = res["text"] or ""
                try:
                    ref_to_use = reference_text or ""
                    hyp_to_use = hyp
                    if ignore_order:
                        ref_to_use = _sort_lines_for_comparison(ref_to_use)
                        hyp_to_use = _sort_lines_for_comparison(hyp_to_use)
                    comp = self.text_comparator.compare_texts(ref_to_use, hyp_to_use)
                    rows.append(
                        {
                            "Модель": model,
                            "WER": comp.wer,
                            "BLEU": comp.bleu_score,
                            "Схожесть": comp.similarity_score,
                            "CharAcc": comp.character_accuracy,
                            "LevDist": comp.levenshtein_distance,
                            "Слова эталон": comp.reference_word_count,
                            "Слова гипотеза": comp.hypothesis_word_count,
                        }
                    )
                except Exception as e:
                    rows.append(
                        {
                            "Модель": model,
                            "WER": None,
                            "BLEU": None,
                            "Схожесть": None,
                            "CharAcc": None,
                            "LevDist": None,
                            "Слова эталон": None,
                            "Слова гипотеза": None,
                            "Ошибка": str(e),
                        }
                    )
            if rows:
                dfm = pd.DataFrame(rows)
                # Удобная сортировка: по WER возрастанию
                try:
                    dfm = dfm.sort_values(
                        by=["WER"], ascending=True, na_position="last"
                    )
                except Exception:
                    pass
                st.markdown("#### Таблица метрик по моделям")
                st.dataframe(dfm, use_container_width=True)
                csvm = dfm.to_csv(index=False).encode("utf-8")
                st.download_button(
                    label="📥 Скачать метрики (CSV)",
                    data=csvm,
                    file_name=f"metrics_{sel_file}.csv",
                    mime="text/csv",
                )

                with st.expander("Показать эталон и гипотезы (нормализованные)"):
                    norm_ref = self.text_comparator._preprocess_text(
                        reference_text or ""
                    )

                    st.markdown("**Эталон (нормализованный):**")
                    st.text(norm_ref)
                    for res in file_block["results"]:
                        st.markdown(f"**{res['model_name']}**")
                        st.text(
                            self.text_comparator._preprocess_text(res.get("text") or "")
                        )

    def _run_model_comparison(
        self,
        audio_data: np.ndarray,
        sample_rate: int,
        model_names: list[str],
        language: str,
        parallel: bool = True,
        max_workers: int = 4,
        timeout: int = 300,
    ) -> list[dict] | None:
        """Запуск сравнения нескольких моделей"""
        results = []

        def transcribe_single_model(model_name: str) -> dict:
            """Транскрибация одной моделью с измерением времени"""
            try:
                # Обновляем язык в транскрибере
                transcriber = self.transcription_manager.get_transcriber(model_name)
                if transcriber:
                    transcriber.language = language

                start_time = time.time()
                result = self.transcription_manager.transcribe(
                    audio_data, sample_rate, model_name
                )
                processing_time = time.time() - start_time

                return {
                    "model_name": model_name,
                    "success": True,
                    "text": result.text if result else "",
                    "processing_time": processing_time,
                    "language": result.language if result else language,
                    "character_count": (
                        len(result.text) if result and result.text else 0
                    ),
                    "word_count": (
                        len(result.text.split()) if result and result.text else 0
                    ),
                    "error": None,
                    "timestamp": datetime.now().strftime("%H:%M:%S"),
                }

            except Exception as e:
                logger.error(f"Ошибка в модели {model_name}: {e}")
                return {
                    "model_name": model_name,
                    "success": False,
                    "text": "",
                    "processing_time": 0,
                    "language": language,
                    "character_count": 0,
                    "word_count": 0,
                    "error": str(e),
                    "timestamp": datetime.now().strftime("%H:%M:%S"),
                }

        try:
            if parallel and len(model_names) > 1:
                # Параллельная обработка
                with concurrent.futures.ThreadPoolExecutor(
                    max_workers=max_workers
                ) as executor:
                    # Создаем прогресс-бар
                    progress_bar = st.progress(0)
                    status_text = st.empty()

                    # Запускаем задачи
                    future_to_model = {
                        executor.submit(transcribe_single_model, model): model
                        for model in model_names
                    }

                    completed = 0
                    for future in concurrent.futures.as_completed(
                        future_to_model, timeout=timeout
                    ):
                        model = future_to_model[future]
                        try:
                            result = future.result(
                                timeout=60
                            )  # Таймаут для каждой задачи
                            results.append(result)
                        except concurrent.futures.TimeoutError:
                            logger.warning(f"Таймаут для модели {model}")
                            results.append(
                                {
                                    "model_name": model,
                                    "success": False,
                                    "text": "",
                                    "processing_time": timeout,
                                    "language": language,
                                    "character_count": 0,
                                    "word_count": 0,
                                    "error": "Timeout exceeded",
                                    "timestamp": datetime.now().strftime("%H:%M:%S"),
                                }
                            )
                        except Exception as e:
                            logger.error(
                                f"Ошибка при получении результата от {model}: {e}"
                            )
                            results.append(
                                {
                                    "model_name": model,
                                    "success": False,
                                    "text": "",
                                    "processing_time": 0,
                                    "language": language,
                                    "character_count": 0,
                                    "word_count": 0,
                                    "error": str(e),
                                    "timestamp": datetime.now().strftime("%H:%M:%S"),
                                }
                            )

                        completed += 1
                        progress = completed / len(model_names)
                        progress_bar.progress(progress)
                        status_text.text(
                            f"Завершено: {completed}/{len(model_names)} моделей"
                        )

                    progress_bar.empty()
                    status_text.empty()

            else:
                # Последовательная обработка
                progress_bar = st.progress(0)
                status_text = st.empty()

                for i, model_name in enumerate(model_names):
                    status_text.text(
                        f"Обработка: {model_name} ({i + 1}/{len(model_names)})"
                    )
                    result = transcribe_single_model(model_name)
                    results.append(result)

                    progress = (i + 1) / len(model_names)
                    progress_bar.progress(progress)

                progress_bar.empty()
                status_text.empty()

            # Сортируем результаты по времени обработки
            results.sort(key=lambda x: x["processing_time"])

            return results

        except Exception as e:
            st.error(f"Ошибка при сравнении моделей: {str(e)}")
            logger.error(f"Ошибка при сравнении моделей: {e}")
            return None

    def _run_model_comparison_stereo(
        self,
        left_channel: np.ndarray,
        right_channel: np.ndarray,
        sample_rate: int,
        model_names: list[str],
        language: str,
        parallel: bool = True,
        max_workers: int = 4,
        timeout: int = 300,
    ) -> list[dict] | None:
        """Запуск сравнения нескольких моделей на стерео-каналах с объединением в диалог."""
        results: list[dict] = []

        def transcribe_single_model(model_name: str) -> dict:
            try:
                transcriber = self.transcription_manager.get_transcriber(model_name)
                if transcriber:
                    transcriber.language = language

                start_time = time.time()
                result = self.transcription_manager.transcribe_stereo_dialog(
                    left_channel,
                    right_channel,
                    sample_rate,
                    model_name,
                )
                processing_time = time.time() - start_time

                return {
                    "model_name": model_name,
                    "success": True,
                    "text": result.text if result else "",
                    "processing_time": processing_time,
                    "language": result.language if result else language,
                    "character_count": (
                        len(result.text) if result and result.text else 0
                    ),
                    "word_count": (
                        len(result.text.split()) if result and result.text else 0
                    ),
                    "error": None,
                    "timestamp": datetime.now().strftime("%H:%M:%S"),
                }
            except Exception as e:
                logger.error(f"Ошибка в модели {model_name} (stereo): {e}")
                return {
                    "model_name": model_name,
                    "success": False,
                    "text": "",
                    "processing_time": 0,
                    "language": language,
                    "character_count": 0,
                    "word_count": 0,
                    "error": str(e),
                    "timestamp": datetime.now().strftime("%H:%M:%S"),
                }

        try:
            if parallel and len(model_names) > 1:
                with concurrent.futures.ThreadPoolExecutor(
                    max_workers=max_workers
                ) as executor:
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    future_to_model = {
                        executor.submit(transcribe_single_model, model): model
                        for model in model_names
                    }
                    completed = 0
                    for future in concurrent.futures.as_completed(
                        future_to_model, timeout=timeout
                    ):
                        model = future_to_model[future]
                        try:
                            res = future.result(timeout=60)
                            results.append(res)
                        except concurrent.futures.TimeoutError:
                            logger.warning(f"Таймаут для модели {model}")
                            results.append(
                                {
                                    "model_name": model,
                                    "success": False,
                                    "text": "",
                                    "processing_time": timeout,
                                    "language": language,
                                    "character_count": 0,
                                    "word_count": 0,
                                    "error": "Timeout exceeded",
                                    "timestamp": datetime.now().strftime("%H:%M:%S"),
                                }
                            )
                        except Exception as e:
                            logger.error(
                                f"Ошибка при получении результата от {model}: {e}"
                            )
                            results.append(
                                {
                                    "model_name": model,
                                    "success": False,
                                    "text": "",
                                    "processing_time": 0,
                                    "language": language,
                                    "character_count": 0,
                                    "word_count": 0,
                                    "error": str(e),
                                    "timestamp": datetime.now().strftime("%H:%M:%S"),
                                }
                            )
                        completed += 1
                        progress_bar.progress(completed / len(model_names))
                        status_text.text(
                            f"Завершено: {completed}/{len(model_names)} моделей"
                        )
                    progress_bar.empty()
                    status_text.empty()
            else:
                progress_bar = st.progress(0)
                status_text = st.empty()
                for i, model_name in enumerate(model_names):
                    status_text.text(
                        f"Обработка: {model_name} ({i + 1}/{len(model_names)})"
                    )
                    res = transcribe_single_model(model_name)
                    results.append(res)
                    progress_bar.progress((i + 1) / len(model_names))
                progress_bar.empty()
                status_text.empty()

            results.sort(key=lambda x: x["processing_time"])
            return results
        except Exception as e:
            st.error(f"Ошибка при сравнении моделей (stereo): {str(e)}")
            logger.error(f"Ошибка при сравнении моделей (stereo): {e}")
            return None

    def _display_model_comparison_results(self):
        """Отображение результатов сравнения моделей"""
        results = st.session_state.model_comparison_results

        if not results:
            return

        st.subheader("📊 Результаты сравнения моделей")

        # Создаем DataFrame для удобного отображения
        df = pd.DataFrame(results)

        # Основные метрики
        col1, col2, col3, col4 = st.columns(4)

        successful_models = df[df["success"] == True]

        with col1:
            st.metric("Успешных моделей", f"{len(successful_models)}/{len(results)}")

        with col2:
            if len(successful_models) > 0:
                avg_time = successful_models["processing_time"].mean()
                st.metric("Среднее время", f"{avg_time:.2f} сек")
            else:
                st.metric("Среднее время", "—")

        with col3:
            if len(successful_models) > 0:
                fastest_model = successful_models.loc[
                    successful_models["processing_time"].idxmin()
                ]
                st.metric("Самая быстрая", fastest_model["model_name"])
            else:
                st.metric("Самая быстрая", "—")

        with col4:
            if len(successful_models) > 0:
                avg_words = successful_models["word_count"].mean()
                st.metric("Среднее слов", f"{avg_words:.0f}")
            else:
                st.metric("Среднее слов", "—")

        # Таблица результатов
        st.subheader("🗂️ Детальные результаты")

        # Подготавливаем данные для отображения
        display_df = df.copy()
        display_df["Статус"] = display_df["success"].apply(
            lambda x: "✅ Успешно" if x else "❌ Ошибка"
        )
        display_df["Время (сек)"] = display_df["processing_time"].round(2)
        display_df["Символов"] = display_df["character_count"]
        display_df["Слов"] = display_df["word_count"]

        # Выбираем нужные колонки для отображения
        columns_to_show = [
            "model_name",
            "Статус",
            "Время (сек)",
            "Символов",
            "Слов",
            "language",
        ]
        table_df = display_df[columns_to_show].rename(
            columns={"model_name": "Модель", "language": "Язык"}
        )

        st.dataframe(table_df, use_container_width=True)

        # График времени обработки
        st.subheader("⚡ Сравнение скорости")

        if len(successful_models) > 0:
            fig_time = px.bar(
                successful_models,
                x="model_name",
                y="processing_time",
                title="Время обработки по моделям",
                labels={"model_name": "Модель", "processing_time": "Время (сек)"},
                color="processing_time",
                color_continuous_scale="RdYlGn_r",
            )
            fig_time.update_layout(xaxis_tickangle=-45)
            st.plotly_chart(fig_time, use_container_width=True)

        # График количества слов
        if len(successful_models) > 0:
            fig_words = px.bar(
                successful_models,
                x="model_name",
                y="word_count",
                title="Количество слов в результатах",
                labels={"model_name": "Модель", "word_count": "Количество слов"},
                color="word_count",
                color_continuous_scale="Blues",
            )
            fig_words.update_layout(xaxis_tickangle=-45)
            st.plotly_chart(fig_words, use_container_width=True)

        # Отображение текстов транскрипции
        st.subheader("📝 Тексты транскрипции")

        for result in results:
            with st.expander(
                f"{result['model_name']} - {result['Время (сек)'] if 'Время (сек)' in result else result['processing_time']:.2f} сек"
            ):
                if result["success"]:
                    st.text_area(
                        f"Текст ({result['character_count']} символов, {result['word_count']} слов)",
                        value=result["text"],
                        height=150,
                        key=f"text_{result['model_name']}",
                    )
                else:
                    st.error(f"Ошибка: {result['error']}")

        # Кнопки экспорта
        st.subheader("💾 Экспорт результатов")

        col1, col2 = st.columns(2)

        with col1:
            # Экспорт в CSV
            csv_data = self._prepare_csv_export(results)
            st.download_button(
                label="📊 Скачать CSV",
                data=csv_data,
                file_name=f"transcription_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv",
            )

        with col2:
            # Экспорт детального отчета
            detailed_report = self._prepare_detailed_report(results)
            st.download_button(
                label="📄 Скачать детальный отчет",
                data=detailed_report,
                file_name=f"detailed_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                mime="text/plain",
            )

    def _prepare_csv_export(self, results: list[dict]) -> str:
        """Подготовка данных для экспорта в CSV"""
        df = pd.DataFrame(results)

        # Выбираем основные поля для экспорта
        export_df = df[
            [
                "model_name",
                "success",
                "processing_time",
                "character_count",
                "word_count",
                "language",
                "error",
                "timestamp",
            ]
        ].copy()

        export_df.columns = [
            "Модель",
            "Успешно",
            "Время_обработки_сек",
            "Количество_символов",
            "Количество_слов",
            "Язык",
            "Ошибка",
            "Время_завершения",
        ]

        return export_df.to_csv(index=False, encoding="utf-8-sig")

    def _prepare_detailed_report(self, results: list[dict]) -> str:
        """Подготовка детального отчета"""
        report_lines = []
        report_lines.append("ДЕТАЛЬНЫЙ ОТЧЕТ СРАВНЕНИЯ МОДЕЛЕЙ ТРАНСКРИБАЦИИ")
        report_lines.append("=" * 60)
        report_lines.append(
            f"Дата и время: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )
        report_lines.append(f"Количество моделей: {len(results)}")

        successful_results = [r for r in results if r["success"]]
        report_lines.append(f"Успешных: {len(successful_results)}")
        report_lines.append("")

        # Сводная статистика
        if successful_results:
            avg_time = sum(r["processing_time"] for r in successful_results) / len(
                successful_results
            )
            report_lines.append("СВОДНАЯ СТАТИСТИКА:")
            report_lines.append(f"Среднее время обработки: {avg_time:.2f} сек")
            report_lines.append(
                f"Самая быстрая модель: {min(successful_results, key=lambda x: x['processing_time'])['model_name']}"
            )
            report_lines.append(
                f"Самая медленная модель: {max(successful_results, key=lambda x: x['processing_time'])['model_name']}"
            )
            report_lines.append("")

        # Детальные результаты
        report_lines.append("ДЕТАЛЬНЫЕ РЕЗУЛЬТАТЫ:")
        report_lines.append("---" * 20)

        for i, result in enumerate(results, 1):
            report_lines.append(f"{i}. {result['model_name']}")
            report_lines.append(
                f"   Статус: {'Успешно' if result['success'] else 'Ошибка'}"
            )
            report_lines.append(
                f"   Время обработки: {result['processing_time']:.2f} сек"
            )

            if result["success"]:
                report_lines.append(f"   Символов: {result['character_count']}")
                report_lines.append(f"   Слов: {result['word_count']}")
                report_lines.append(
                    f"   Текст: {result['text'][:100]}{'...' if len(result['text']) > 100 else ''}"
                )
            else:
                report_lines.append(f"   Ошибка: {result['error']}")

            report_lines.append("")

        return "\n".join(report_lines)


def main():
    """Основная функция запуска приложения"""
    try:
        app = AudioAnalysisApp()
        app.run()
    except Exception as e:
        st.error(f"Критическая ошибка приложения: {str(e)}")
        logger.error(f"Критическая ошибка: {e}", exc_info=True)


if __name__ == "__main__":
    main()
