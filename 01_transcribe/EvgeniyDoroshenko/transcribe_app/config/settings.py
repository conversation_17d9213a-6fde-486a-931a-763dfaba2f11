# Базовые настройки Streamlit
STREAMLIT_CONFIG = {
    "page_title": "Анализатор аудиозаписей",
    "page_icon": "🎙️",
    "layout": "wide",
    "initial_sidebar_state": "expanded",
}

# Методы улучшения аудио
ENHANCEMENT_METHODS = {
    "ffmpeg": {
        "name": "FFmpeg",
        "description": "Обработка аудио с помощью FFmpeg (нормализация громкости и частотная фильтрация)",
        "params": {
            "highpass_freq": 120,
            "lowpass_freq": 2500,
            "loudnorm_i": -23.0,
            "loudnorm_tp": -3.0,
            "loudnorm_lra": 6.0,
            "target_sample_rate": 16000,
            "sample_fmt": 's16',
        },
    },
    "noisereduce": {
        "name": "NoiseReduce",
        "description": "Спектральное подавление шума на основе статистических методов",
        "params": {"stationary": True, "prop_decrease": 0.8},
    },
    "spectral_subtraction": {
        "name": "Спектральное вычитание",
        "description": "Классический метод подавления шума путем вычитания спектра шума",
        "params": {"alpha": 2.0, "beta": 0.01},
    },
    "wiener_filter": {
        "name": "Фильтр Винера",
        "description": "Адаптивный фильтр для минимизации среднеквадратичной ошибки",
        "params": {},
    },
    "deepfilternet": {
        "name": "DeepFilterNet",
        "description": "Нейросетевое подавление шума в реальном времени",
        "params": {},
    },
    "rnnoise": {
        "name": "RNNoise",
        "description": "Рекуррентная нейронная сеть для подавления шума",
        "params": {},
    },
}