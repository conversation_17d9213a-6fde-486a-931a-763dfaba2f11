name: "streaming_acoustic"
platform: "tensorrt_plan"
max_batch_size: 16

input [
  {
    name: "signal"
    data_type: TYPE_INT32
    dims: [ 2400, 1 ]
  }
]
input [
  {
    name: "state"
    data_type: TYPE_FP16
    dims: [ 219729 ]
  }
]

output [
  {
    name: "logprobs"
    data_type: TYPE_FP32
    dims: [ 10, 35 ]
  }
]
output [
  {
    name: "state_next"
    data_type: TYPE_FP16
    dims: [ 219729 ]
  }
]

dynamic_batching {
  max_queue_delay_microseconds: 10000
}

instance_group [
    {
      count: 1
      kind: KIND_GPU
      gpus: [ 0 ]
    }
]

model_warmup [

    {
        name: "warmup_sample_1"
        batch_size: 1
        count: 10
        inputs: {
          key: "signal",
          value: {
              data_type: TYPE_INT32
              dims: [ 2400, 1 ]
              zero_data: true
          }
        }
        inputs: {
          key: "state",
          value: {
              data_type: TYPE_FP16
              dims: [ 219729 ]
              zero_data: true
          }
        }
    },

    {
        name: "warmup_sample_2"
        batch_size: 16
        count: 10
        inputs: {
          key: "signal",
          value: {
              data_type: TYPE_INT32
              dims: [ 2400, 1 ]
              zero_data: true
          }
        }
        inputs: {
          key: "state",
          value: {
              data_type: TYPE_FP16
              dims: [ 219729 ]
              zero_data: true
          }
        }
    }
]