[project]
name = "tone"
version = "0.1.0"
description = "A demonstration of T-one – a streaming CTC-based ASR pipeline for Russian"
authors = [
    {name = "T-Software DC"}
]
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "numpy (>=1.17.3,<3.0.0)",
    "huggingface-hub (>=0.14.0,<1.0.0)",
    "onnxruntime (>=1.12.0,<1.20.0) ; python_version >= '3.9' and python_version < '3.10'",
    "onnxruntime (>=1.12.0,<2.0.0) ; python_version >= '3.10'",
    "pyctcdecode (>=0.2.0,<1.0.0)",
    "kenlm (>=0.2.0,<1.0.0)",
]


[project.optional-dependencies]
demo = [
    "miniaudio (>=1.50,<2.0)",
    "fastapi (>=0.100.0,<1.0.0)",
    "uvicorn[standard] (>=0.22.0,<1.0.0)",
]

finetune = [
    "ipykernel (>=6.19.0,<7.0.0)",
    "transformers (>=4.30.0,<5.0.0)",
    "accelerate (>=0.26.0,<1.0.0)",
    "datasets (>=2.14.0,<4.0.0)",  # Newer versions require ffmpeg v4 which can be hard to install
    "librosa (>=0.9.2,<1.0.0)",
    "soundfile (>=0.13.1,<1.0.0)",
    "evaluate (>=0.4.0,<1.0.0)",
    "jiwer (>=3.0.3,<5.0.0)",
    "omegaconf (>=2.3.0,<3.0.0)",
    "cloudpathlib (>=0.16.0,<1.0.0)",
    "torch (>=2.7.0,<3.0.0)",
    "torchaudio (>=2.7.0,<3.0.0)",
]

[project.scripts]
tone = 'tone.__main__:main'

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.dev.dependencies]
ruff = "^0.11.6"
mypy = "^1.14.0"

[tool.ruff]
line-length = 120

[tool.ruff.lint]
select = [
    "ALL",
]
extend-ignore = [
    "ANN003",  # Missing type annotation for `**kwargs`
    "ANN401",  # Dynamically typed expressions (typing.Any) are disallowed
    "D107",  # Missing docstring in `__init__`
    "D203",  # incorrect-blank-line-before-class
    "D401",  # First line of docstring should be in imperative mood
    "D417",  # Missing argument descriptions in the docstring for `**kwargs`, `*args`
    "EM101",  # Exception must not use a string literal, assign to variable first
    "EM102",  # Exception must not use an f-string literal, assign to variable first
    "FBT001",  # Boolean-typed positional argument in function definition
    "FBT002",  # Boolean default positional argument in function definition
    "N812",  # Lowercase imported as non-lowercase
    "PLR0913",  # Too many arguments in function definition
    "PLR2004",  # Magic value used
    "RUF001",  # String contains ambiguous `х`
    "S101",  # Use of `assert` detected
    "T201",  # `print` found
    "TRY003",  # Avoid specifying long messages outside the exception class
]


[[tool.mypy.overrides]]
module = ["wavio.*", "onnxruntime.*"]
follow_untyped_imports = true
