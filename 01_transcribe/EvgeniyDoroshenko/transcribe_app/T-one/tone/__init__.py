"""Package for the demonstration of T-one — a streaming CTC-based ASR pipeline for Russian."""

from .decoder import BeamSearchCTCDecoder, DecoderType, GreedyCTCDecoder
from .demo import read_audio, read_example_audio, read_stream_example_audio
from .logprob_splitter import LogprobPhrase, StreamingLogprobSplitter
from .onnx_wrapper import StreamingCTCModel
from .pipeline import StreamingCTCPipeline, TextPhrase
from .project import VERSION

__all__ = [
    "BeamSearchCTCDecoder",
    "DecoderType",
    "GreedyCTCDecoder",
    "LogprobPhrase",
    "StreamingCTCModel",
    "StreamingCTCPipeline",
    "StreamingLogprobSplitter",
    "TextPhrase",
    "read_audio",
    "read_example_audio",
    "read_stream_example_audio",
]
__version__ = VERSION
