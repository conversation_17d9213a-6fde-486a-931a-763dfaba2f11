[project]
name = "transcript"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12,<3.13"
dependencies = [
    "colorlog>=6.9.0",
    "deepfilternet>=0.5.6",
    "faster-whisper>=1.1.0",
    "ffmpeg-python>=0.2.0",
    "gigaam>=0.1.0",
    "jiwer>=3.1.0",
    "librosa>=0.11.0",
    "nemo-toolkit[asr]>=2.4.0",
    "nltk>=3.9.1",
    "noisereduce>=3.0.3",
    "numpy>=1.26.4",
    "openai-whisper>=20250625",
    "plotly>=6.3.0",
    "pyannote-audio>=3.3.2",
    "pydub>=0.25.1",
    "python-dotenv>=1.1.1",
    "python-levenshtein>=0.27.1",
    "pyyaml>=6.0.2",
    "resampy>=0.4.3",
    "rnnoise-wrapper",
    "scipy>=1.16.1",
    "soundfile>=0.13.1",
    "speechbrain>=1.0.3",
    "streamlit>=1.48.1",
    "transformers>=4.51.3",
    "vosk>=0.3.45",
    "whisperx>=3.3.1",
]

[tool.uv.sources]
rnnoise-wrapper = { git = "https://github.com/Desklop/RNNoise_Wrapper" }

[dependency-groups]
dev = [
    "black>=25.1.0",
]
